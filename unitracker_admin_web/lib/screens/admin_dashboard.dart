import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../config/app_theme.dart';
import '../services/analytics_service.dart';
import '../services/export_service.dart';

class AdminDashboard extends StatefulWidget {
  const AdminDashboard({super.key});

  @override
  State<AdminDashboard> createState() => _AdminDashboardState();
}

class _AdminDashboardState extends State<AdminDashboard> {
  final AnalyticsService _analyticsService = AnalyticsService();
  final ExportService _exportService = ExportService();
  
  Map<String, dynamic> _systemAnalytics = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadAnalytics();
  }

  Future<void> _loadAnalytics() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final analytics = await _analyticsService.getSystemAnalytics();
      setState(() {
        _systemAnalytics = analytics;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('UniTracker Admin Dashboard'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadAnalytics,
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'export_users',
                child: ListTile(
                  leading: Icon(Icons.people),
                  title: Text('Export Users'),
                ),
              ),
              const PopupMenuItem(
                value: 'export_reservations',
                child: ListTile(
                  leading: Icon(Icons.event_seat),
                  title: Text('Export Reservations'),
                ),
              ),
              const PopupMenuItem(
                value: 'export_analytics',
                child: ListTile(
                  leading: Icon(Icons.analytics),
                  title: Text('Export Analytics'),
                ),
              ),
              const PopupMenuItem(
                value: 'settings',
                child: ListTile(
                  leading: Icon(Icons.settings),
                  title: Text('Settings'),
                ),
              ),
            ],
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildWelcomeCard(),
                  const SizedBox(height: 24),
                  _buildStatsGrid(),
                  const SizedBox(height: 24),
                  _buildQuickActions(),
                  const SizedBox(height: 24),
                  _buildRecentActivity(),
                ],
              ),
            ),
    );
  }

  Widget _buildWelcomeCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: AppTheme.primaryGradient,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryColor.withOpacity(0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Welcome to UniTracker Admin',
            style: TextStyle(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Manage your university bus system',
            style: TextStyle(
              fontSize: 16,
              color: Colors.white70,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              const Icon(Icons.admin_panel_settings, color: Colors.white70),
              const SizedBox(width: 8),
              Text(
                'System Status: ${_systemAnalytics.isNotEmpty ? "Online" : "Loading..."}',
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.white70,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatsGrid() {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 4,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 1.2,
      children: [
        _buildStatCard(
          'Total Users',
          _systemAnalytics['total_users']?.toString() ?? '0',
          Icons.people,
          AppTheme.primaryColor,
          '${_systemAnalytics['users_by_role']?['student'] ?? 0} students',
        ),
        _buildStatCard(
          'Active Buses',
          _systemAnalytics['active_buses']?.toString() ?? '0',
          Icons.directions_bus,
          AppTheme.successColor,
          '${_systemAnalytics['total_buses'] ?? 0} total',
        ),
        _buildStatCard(
          'Routes',
          _systemAnalytics['total_routes']?.toString() ?? '0',
          Icons.route,
          AppTheme.warningColor,
          '${_systemAnalytics['active_routes'] ?? 0} active',
        ),
        _buildStatCard(
          'Reservations',
          _systemAnalytics['total_reservations']?.toString() ?? '0',
          Icons.event_seat,
          AppTheme.errorColor,
          'Today',
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color, String subtitle) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: color, size: 24),
                ),
                const Spacer(),
                Text(
                  value,
                  style: AppTheme.headingMedium.copyWith(
                    color: color,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(title, style: AppTheme.labelLarge),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: AppTheme.bodySmall.copyWith(color: Colors.grey[600]),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Quick Actions', style: AppTheme.headingSmall),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 3,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 2,
          children: [
            _buildActionCard('Manage Users', Icons.people, () => _showComingSoon('User Management')),
            _buildActionCard('Manage Buses', Icons.directions_bus, () => _showComingSoon('Bus Management')),
            _buildActionCard('Manage Routes', Icons.route, () => _showComingSoon('Route Management')),
            _buildActionCard('View Analytics', Icons.analytics, () => _showComingSoon('Advanced Analytics')),
            _buildActionCard('Send Notifications', Icons.notifications, () => _showComingSoon('Notification Center')),
            _buildActionCard('System Settings', Icons.settings, () => _showComingSoon('System Settings')),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard(String title, IconData icon, VoidCallback onTap) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, color: AppTheme.primaryColor, size: 32),
              const SizedBox(height: 8),
              Text(
                title,
                style: AppTheme.labelMedium,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRecentActivity() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Recent Activity', style: AppTheme.headingSmall),
        const SizedBox(height: 16),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                _buildActivityItem(
                  'New user registered',
                  'Ahmed Hassan joined as a student',
                  Icons.person_add,
                  AppTheme.successColor,
                  '2 hours ago',
                ),
                const Divider(),
                _buildActivityItem(
                  'Bus route updated',
                  'Alexandria Route schedule modified',
                  Icons.route,
                  AppTheme.warningColor,
                  '4 hours ago',
                ),
                const Divider(),
                _buildActivityItem(
                  'System maintenance',
                  'Database backup completed successfully',
                  Icons.backup,
                  AppTheme.infoColor,
                  '6 hours ago',
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildActivityItem(String title, String subtitle, IconData icon, Color color, String time) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: color.withOpacity(0.1),
        child: Icon(icon, color: color, size: 20),
      ),
      title: Text(title, style: AppTheme.labelMedium),
      subtitle: Text(subtitle, style: AppTheme.bodySmall),
      trailing: Text(time, style: AppTheme.bodySmall.copyWith(color: Colors.grey[600])),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'export_users':
        _showComingSoon('Export Users');
        break;
      case 'export_reservations':
        _showComingSoon('Export Reservations');
        break;
      case 'export_analytics':
        _showComingSoon('Export Analytics');
        break;
      case 'settings':
        _showComingSoon('Settings');
        break;
    }
  }

  void _showComingSoon(String feature) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(feature),
        content: const Text('This feature is coming soon! The admin panel will include comprehensive management tools.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
