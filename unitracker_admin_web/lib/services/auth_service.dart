import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/user_model.dart';
import '../config/supabase_config.dart';

class AuthService {
  final SupabaseClient _supabase = Supabase.instance.client;

  Future<void> signIn({
    required String email,
    required String password,
  }) async {
    try {
      final response = await _supabase.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (response.user == null) {
        throw Exception('Sign in failed');
      }
    } on AuthException catch (e) {
      throw Exception(e.message);
    } catch (e) {
      throw Exception('An unexpected error occurred: $e');
    }
  }

  Future<void> signUp({
    required String email,
    required String password,
    required String fullName,
    required UserRole role,
    String? studentId,
    String? university,
    String? department,
    String? phone,
    String? driverLicense,
    DateTime? licenseExpiry,
  }) async {
    try {
      final response = await _supabase.auth.signUp(
        email: email,
        password: password,
      );

      if (response.user == null) {
        throw Exception('Sign up failed');
      }

      // Create user profile
      await _createUserProfile(
        userId: response.user!.id,
        email: email,
        fullName: fullName,
        role: role,
        studentId: studentId,
        university: university,
        department: department,
        phone: phone,
        driverLicense: driverLicense,
        licenseExpiry: licenseExpiry,
      );
    } on AuthException catch (e) {
      throw Exception(e.message);
    } catch (e) {
      throw Exception('An unexpected error occurred: $e');
    }
  }

  Future<void> _createUserProfile({
    required String userId,
    required String email,
    required String fullName,
    required UserRole role,
    String? studentId,
    String? university,
    String? department,
    String? phone,
    String? driverLicense,
    DateTime? licenseExpiry,
  }) async {
    try {
      await _supabase.from(SupabaseConfig.usersTable).insert({
        'id': userId,
        'email': email,
        'full_name': fullName,
        'role': role.name,
        'student_id': studentId,
        'university': university,
        'department': department,
        'phone': phone,
        'driver_license': driverLicense,
        'license_expiry': licenseExpiry?.toIso8601String(),
        'is_active': true,
      });
    } catch (e) {
      throw Exception('Failed to create user profile: $e');
    }
  }

  Future<UserModel> getUserProfile(String userId) async {
    try {
      final response = await _supabase
          .from(SupabaseConfig.usersTable)
          .select()
          .eq('id', userId)
          .single();

      return UserModel.fromJson(response);
    } catch (e) {
      throw Exception('Failed to load user profile: $e');
    }
  }

  Future<UserModel> updateProfile({
    required String userId,
    String? fullName,
    String? phone,
    String? university,
    String? department,
    String? profileImageUrl,
  }) async {
    try {
      final updateData = <String, dynamic>{};
      
      if (fullName != null) updateData['full_name'] = fullName;
      if (phone != null) updateData['phone'] = phone;
      if (university != null) updateData['university'] = university;
      if (department != null) updateData['department'] = department;
      if (profileImageUrl != null) updateData['profile_image_url'] = profileImageUrl;
      
      updateData['updated_at'] = DateTime.now().toIso8601String();

      final response = await _supabase
          .from(SupabaseConfig.usersTable)
          .update(updateData)
          .eq('id', userId)
          .select()
          .single();

      return UserModel.fromJson(response);
    } catch (e) {
      throw Exception('Failed to update profile: $e');
    }
  }

  Future<void> signOut() async {
    try {
      await _supabase.auth.signOut();
    } catch (e) {
      throw Exception('Failed to sign out: $e');
    }
  }

  Future<void> resetPassword(String email) async {
    try {
      await _supabase.auth.resetPasswordForEmail(email);
    } on AuthException catch (e) {
      throw Exception(e.message);
    } catch (e) {
      throw Exception('Failed to send reset password email: $e');
    }
  }

  Future<List<UserModel>> getAllUsers() async {
    try {
      final response = await _supabase
          .from(SupabaseConfig.usersTable)
          .select()
          .order('created_at', ascending: false);

      return (response as List)
          .map((user) => UserModel.fromJson(user))
          .toList();
    } catch (e) {
      throw Exception('Failed to load users: $e');
    }
  }

  Future<List<UserModel>> getUsersByRole(UserRole role) async {
    try {
      final response = await _supabase
          .from(SupabaseConfig.usersTable)
          .select()
          .eq('role', role.name)
          .eq('is_active', true)
          .order('full_name', ascending: true);

      return (response as List)
          .map((user) => UserModel.fromJson(user))
          .toList();
    } catch (e) {
      throw Exception('Failed to load users by role: $e');
    }
  }

  User? get currentUser => _supabase.auth.currentUser;
  Session? get currentSession => _supabase.auth.currentSession;
}
