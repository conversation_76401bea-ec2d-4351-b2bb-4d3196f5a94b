import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:csv/csv.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:flutter/foundation.dart';
import 'package:file_picker/file_picker.dart';
import '../models/reservation_model.dart';
import '../models/bus_model.dart';
import '../models/route_model.dart';
import '../models/user_model.dart';

class ExportService {
  static final ExportService _instance = ExportService._internal();
  factory ExportService() => _instance;
  ExportService._internal();

  // Export reservations to CSV
  Future<String?> exportReservationsToCSV(List<ReservationModel> reservations) async {
    try {
      final List<List<dynamic>> csvData = [
        // Header row
        [
          'ID',
          'User Name',
          'Route Name',
          'Bus Number',
          'Reservation Date',
          'Seat Number',
          'Status',
          'Pickup Stop',
          'Drop Stop',
          'Created At',
          'Notes'
        ],
        // Data rows
        ...reservations.map((reservation) => [
          reservation.id,
          reservation.user?.fullName ?? 'Unknown',
          reservation.route?.name ?? 'Unknown',
          reservation.bus?.busNumber ?? 'N/A',
          reservation.reservationDate.toIso8601String().split('T')[0],
          reservation.seatNumber ?? 'N/A',
          reservation.status.displayName,
          reservation.pickupStop?.name ?? 'N/A',
          reservation.dropStop?.name ?? 'N/A',
          reservation.createdAt.toIso8601String(),
          reservation.notes ?? '',
        ]),
      ];

      final String csv = const ListToCsvConverter().convert(csvData);
      
      if (kIsWeb) {
        return csv;
      } else {
        final String? outputFile = await FilePicker.platform.saveFile(
          dialogTitle: 'Save Reservations CSV',
          fileName: 'reservations_${DateTime.now().millisecondsSinceEpoch}.csv',
        );
        
        if (outputFile != null) {
          final file = File(outputFile);
          await file.writeAsString(csv);
          return outputFile;
        }
      }
      
      return null;
    } catch (e) {
      print('Error exporting reservations to CSV: $e');
      return null;
    }
  }

  // Export buses to CSV
  Future<String?> exportBusesToCSV(List<BusModel> buses) async {
    try {
      final List<List<dynamic>> csvData = [
        // Header row
        [
          'ID',
          'Bus Number',
          'Plate Number',
          'Capacity',
          'Status',
          'Route Name',
          'Driver Name',
          'Last Maintenance',
          'Next Maintenance',
          'Created At'
        ],
        // Data rows
        ...buses.map((bus) => [
          bus.id,
          bus.busNumber,
          bus.plateNumber,
          bus.capacity,
          bus.status.displayName,
          bus.route?.name ?? 'No route assigned',
          bus.driver?.fullName ?? 'No driver assigned',
          bus.lastMaintenance?.toIso8601String().split('T')[0] ?? 'N/A',
          bus.nextMaintenance?.toIso8601String().split('T')[0] ?? 'N/A',
          bus.createdAt.toIso8601String(),
        ]),
      ];

      final String csv = const ListToCsvConverter().convert(csvData);
      
      if (kIsWeb) {
        return csv;
      } else {
        final String? outputFile = await FilePicker.platform.saveFile(
          dialogTitle: 'Save Buses CSV',
          fileName: 'buses_${DateTime.now().millisecondsSinceEpoch}.csv',
        );
        
        if (outputFile != null) {
          final file = File(outputFile);
          await file.writeAsString(csv);
          return outputFile;
        }
      }
      
      return null;
    } catch (e) {
      print('Error exporting buses to CSV: $e');
      return null;
    }
  }

  // Export routes to CSV
  Future<String?> exportRoutesToCSV(List<RouteModel> routes) async {
    try {
      final List<List<dynamic>> csvData = [
        // Header row
        [
          'ID',
          'Name',
          'Pickup Location',
          'Drop Location',
          'Distance (km)',
          'Frequency (minutes)',
          'Start Time',
          'End Time',
          'Is Active',
          'Color Code',
          'Created At'
        ],
        // Data rows
        ...routes.map((route) => [
          route.id,
          route.name,
          route.pickupLocation,
          route.dropLocation,
          route.distance ?? 'N/A',
          route.frequencyMinutes,
          route.startTime,
          route.endTime,
          route.isActive ? 'Yes' : 'No',
          route.colorCode,
          route.createdAt.toIso8601String(),
        ]),
      ];

      final String csv = const ListToCsvConverter().convert(csvData);
      
      if (kIsWeb) {
        return csv;
      } else {
        final String? outputFile = await FilePicker.platform.saveFile(
          dialogTitle: 'Save Routes CSV',
          fileName: 'routes_${DateTime.now().millisecondsSinceEpoch}.csv',
        );
        
        if (outputFile != null) {
          final file = File(outputFile);
          await file.writeAsString(csv);
          return outputFile;
        }
      }
      
      return null;
    } catch (e) {
      print('Error exporting routes to CSV: $e');
      return null;
    }
  }

  // Export users to CSV
  Future<String?> exportUsersToCSV(List<UserModel> users) async {
    try {
      final List<List<dynamic>> csvData = [
        // Header row
        [
          'ID',
          'Full Name',
          'Email',
          'Role',
          'Student ID',
          'University',
          'Department',
          'Phone Number',
          'Created At'
        ],
        // Data rows
        ...users.map((user) => [
          user.id,
          user.fullName,
          user.email,
          user.role.name,
          user.studentId ?? 'N/A',
          user.university ?? 'N/A',
          user.department ?? 'N/A',
          user.phone ?? 'N/A',
          user.createdAt.toIso8601String(),
        ]),
      ];

      final String csv = const ListToCsvConverter().convert(csvData);
      
      if (kIsWeb) {
        return csv;
      } else {
        final String? outputFile = await FilePicker.platform.saveFile(
          dialogTitle: 'Save Users CSV',
          fileName: 'users_${DateTime.now().millisecondsSinceEpoch}.csv',
        );
        
        if (outputFile != null) {
          final file = File(outputFile);
          await file.writeAsString(csv);
          return outputFile;
        }
      }
      
      return null;
    } catch (e) {
      print('Error exporting users to CSV: $e');
      return null;
    }
  }

  // Export reservations to PDF
  Future<Uint8List?> exportReservationsToPDF(List<ReservationModel> reservations) async {
    try {
      final pdf = pw.Document();

      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          build: (pw.Context context) {
            return [
              pw.Header(
                level: 0,
                child: pw.Text(
                  'UniTracker - Reservations Report',
                  style: pw.TextStyle(fontSize: 24, fontWeight: pw.FontWeight.bold),
                ),
              ),
              pw.SizedBox(height: 20),
              pw.Text(
                'Generated on: ${DateTime.now().toIso8601String().split('T')[0]}',
                style: const pw.TextStyle(fontSize: 12),
              ),
              pw.Text(
                'Total Reservations: ${reservations.length}',
                style: const pw.TextStyle(fontSize: 12),
              ),
              pw.SizedBox(height: 20),
              pw.Table.fromTextArray(
                headers: [
                  'User',
                  'Route',
                  'Date',
                  'Seat',
                  'Status',
                ],
                data: reservations.map((reservation) => [
                  reservation.user?.fullName ?? 'Unknown',
                  reservation.route?.name ?? 'Unknown',
                  reservation.reservationDate.toIso8601String().split('T')[0],
                  reservation.seatNumber ?? 'N/A',
                  reservation.status.displayName,
                ]).toList(),
                headerStyle: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                cellAlignment: pw.Alignment.centerLeft,
              ),
            ];
          },
        ),
      );

      return await pdf.save();
    } catch (e) {
      print('Error exporting reservations to PDF: $e');
      return null;
    }
  }

  // Export analytics report to PDF
  Future<Uint8List?> exportAnalyticsReportToPDF(Map<String, dynamic> analytics) async {
    try {
      final pdf = pw.Document();

      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          build: (pw.Context context) {
            return [
              pw.Header(
                level: 0,
                child: pw.Text(
                  'UniTracker - Analytics Report',
                  style: pw.TextStyle(fontSize: 24, fontWeight: pw.FontWeight.bold),
                ),
              ),
              pw.SizedBox(height: 20),
              pw.Text(
                'Generated on: ${DateTime.now().toIso8601String().split('T')[0]}',
                style: const pw.TextStyle(fontSize: 12),
              ),
              pw.SizedBox(height: 20),
              pw.Text(
                'System Overview',
                style: pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold),
              ),
              pw.SizedBox(height: 10),
              pw.Table.fromTextArray(
                headers: ['Metric', 'Value'],
                data: [
                  ['Total Users', analytics['total_users']?.toString() ?? '0'],
                  ['Total Buses', analytics['total_buses']?.toString() ?? '0'],
                  ['Total Routes', analytics['total_routes']?.toString() ?? '0'],
                  ['Total Reservations', analytics['total_reservations']?.toString() ?? '0'],
                  ['Active Buses', analytics['active_buses']?.toString() ?? '0'],
                  ['Active Routes', analytics['active_routes']?.toString() ?? '0'],
                ],
                headerStyle: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                cellAlignment: pw.Alignment.centerLeft,
              ),
            ];
          },
        ),
      );

      return await pdf.save();
    } catch (e) {
      print('Error exporting analytics to PDF: $e');
      return null;
    }
  }

  // Export all data as JSON backup
  Future<String?> exportFullBackupToJSON(Map<String, dynamic> allData) async {
    try {
      final jsonString = const JsonEncoder.withIndent('  ').convert(allData);
      
      if (kIsWeb) {
        return jsonString;
      } else {
        final String? outputFile = await FilePicker.platform.saveFile(
          dialogTitle: 'Save Full Backup',
          fileName: 'unitracker_backup_${DateTime.now().millisecondsSinceEpoch}.json',
        );
        
        if (outputFile != null) {
          final file = File(outputFile);
          await file.writeAsString(jsonString);
          return outputFile;
        }
      }
      
      return null;
    } catch (e) {
      print('Error exporting full backup: $e');
      return null;
    }
  }

  // Helper method to download file on web
  void downloadFileOnWeb(String content, String fileName, String mimeType) {
    if (kIsWeb) {
      // Web download implementation would go here
      // This is a placeholder for web file download
      print('Downloading $fileName on web...');
    }
  }

  // Helper method to download PDF on web
  void downloadPDFOnWeb(Uint8List pdfBytes, String fileName) {
    if (kIsWeb) {
      // Web PDF download implementation would go here
      print('Downloading PDF $fileName on web...');
    }
  }
}
