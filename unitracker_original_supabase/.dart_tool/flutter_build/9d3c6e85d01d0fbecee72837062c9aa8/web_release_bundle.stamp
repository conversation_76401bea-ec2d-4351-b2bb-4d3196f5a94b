{"inputs": ["C:\\Users\\<USER>\\Desktop\\UniTracker\\frontend\\unitracker_front-end\\unitracker\\pubspec.yaml", "C:\\Users\\<USER>\\Desktop\\UniTracker\\frontend\\unitracker_front-end\\unitracker\\.dart_tool\\flutter_build\\9d3c6e85d01d0fbecee72837062c9aa8\\main.dart.js", "C:\\Users\\<USER>\\Desktop\\UniTracker\\frontend\\unitracker_front-end\\unitracker\\pubspec.yaml", "C:\\Users\\<USER>\\Desktop\\UniTracker\\frontend\\unitracker_front-end\\unitracker\\assets\\images\\bus_location.png", "C:\\Users\\<USER>\\Desktop\\UniTracker\\frontend\\unitracker_front-end\\unitracker\\assets\\images\\bus_logo.png", "C:\\Users\\<USER>\\Desktop\\UniTracker\\frontend\\unitracker_front-end\\unitracker\\assets\\images\\bus_marker.png", "C:\\Users\\<USER>\\Desktop\\UniTracker\\frontend\\unitracker_front-end\\unitracker\\assets\\images\\location_pin.png", "C:\\Users\\<USER>\\Desktop\\UniTracker\\frontend\\unitracker_front-end\\unitracker\\assets\\images\\logo.png", "C:\\Users\\<USER>\\Desktop\\UniTracker\\frontend\\unitracker_front-end\\unitracker\\assets\\images\\logo2.png", "C:\\Users\\<USER>\\Desktop\\UniTracker\\frontend\\unitracker_front-end\\unitracker\\assets\\images\\stop_marker.png", "C:\\Users\\<USER>\\Desktop\\UniTracker\\frontend\\unitracker_front-end\\unitracker\\assets\\fonts\\Poppins\\Poppins-Regular.ttf", "C:\\Users\\<USER>\\Desktop\\UniTracker\\frontend\\unitracker_front-end\\unitracker\\assets\\fonts\\Poppins\\Poppins-Medium.ttf", "C:\\Users\\<USER>\\Desktop\\UniTracker\\frontend\\unitracker_front-end\\unitracker\\assets\\fonts\\Poppins\\Poppins-SemiBold.ttf", "C:\\Users\\<USER>\\Desktop\\UniTracker\\frontend\\unitracker_front-end\\unitracker\\assets\\fonts\\Poppins\\Poppins-Bold.ttf", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cupertino_icons-1.0.8\\assets\\CupertinoIcons.ttf", "C:\\Users\\<USER>\\Documents\\flutter_windows_3.29.3-stable\\flutter\\bin\\cache\\artifacts\\material_fonts\\MaterialIcons-Regular.otf", "C:\\Users\\<USER>\\Documents\\flutter_windows_3.29.3-stable\\flutter\\packages\\flutter\\lib\\src\\material\\shaders\\ink_sparkle.frag", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\boolean_selector-2.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.4.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\clock-1.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cross_file-0.3.4+2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\csslib-1.0.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cupertino_icons-1.0.8\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fake_async-1.3.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\ffi-2.1.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_selector_linux-0.9.3+2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_selector_macos-0.9.4+2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_selector_platform_interface-2.6.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_selector_windows-0.9.3+4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_lints-3.0.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_plugin_android_lifecycle-2.0.28\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_screenutil-5.9.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_maps-8.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_maps_flutter-2.12.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_maps_flutter_android-2.16.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_maps_flutter_ios-2.15.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_maps_flutter_platform_interface-2.11.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_maps_flutter_web-0.5.12\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\html-0.15.6\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.3.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_picker-0.8.9\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_picker_android-0.8.12+23\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_picker_for_web-2.2.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_picker_ios-0.8.12+2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_picker_linux-0.2.1+2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_picker_macos-0.2.1+2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_picker_platform_interface-2.10.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_picker_windows-0.2.1+1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.18.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\js-0.6.7\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\leak_tracker-10.0.9\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\leak_tracker_flutter_testing-3.0.9\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\leak_tracker_testing-3.0.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\lints-3.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\location-5.0.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\location_platform_interface-3.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\location_web-4.2.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\matcher-0.12.17\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\meta-1.16.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mime-1.0.6\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mocktail-1.0.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\nested-1.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_linux-2.2.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler-11.4.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_android-12.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_apple-9.4.7\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_html-0.1.3+5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_platform_interface-4.3.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_windows-0.2.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\platform-3.1.6\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\plugin_platform_interface-2.1.8\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\provider-6.1.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sanitize_html-2.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences-2.5.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_foundation-2.5.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_linux-2.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_platform_interface-2.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_web-2.4.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_windows-2.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stack_trace-1.12.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stream_channel-2.1.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stream_transform-2.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\term_glyph-1.2.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\test_api-0.7.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\typed_data-1.4.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher-6.3.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_android-6.3.16\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_ios-6.3.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_linux-3.2.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_macos-3.2.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_platform_interface-2.3.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_web-2.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_windows-3.1.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vm_service-15.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-0.5.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xdg_directories-1.1.0\\LICENSE", "C:\\Users\\<USER>\\Documents\\flutter_windows_3.29.3-stable\\flutter\\bin\\cache\\pkg\\sky_engine\\LICENSE", "C:\\Users\\<USER>\\Documents\\flutter_windows_3.29.3-stable\\flutter\\packages\\flutter\\LICENSE", "C:\\Users\\<USER>\\Desktop\\UniTracker\\frontend\\unitracker_front-end\\unitracker\\DOES_NOT_EXIST_RERUN_FOR_WILDCARD280351692", "C:\\Users\\<USER>\\Desktop\\UniTracker\\frontend\\unitracker_front-end\\unitracker\\web\\favicon.png", "C:\\Users\\<USER>\\Desktop\\UniTracker\\frontend\\unitracker_front-end\\unitracker\\web\\icons\\Icon-192.png", "C:\\Users\\<USER>\\Desktop\\UniTracker\\frontend\\unitracker_front-end\\unitracker\\web\\icons\\Icon-512.png", "C:\\Users\\<USER>\\Desktop\\UniTracker\\frontend\\unitracker_front-end\\unitracker\\web\\icons\\Icon-maskable-192.png", "C:\\Users\\<USER>\\Desktop\\UniTracker\\frontend\\unitracker_front-end\\unitracker\\web\\icons\\Icon-maskable-512.png", "C:\\Users\\<USER>\\Desktop\\UniTracker\\frontend\\unitracker_front-end\\unitracker\\web\\index.html", "C:\\Users\\<USER>\\Desktop\\UniTracker\\frontend\\unitracker_front-end\\unitracker\\web\\manifest.json"], "outputs": ["C:\\Users\\<USER>\\Desktop\\UniTracker\\frontend\\unitracker_front-end\\unitracker\\build\\web\\main.dart.js", "C:\\Users\\<USER>\\Desktop\\UniTracker\\frontend\\unitracker_front-end\\unitracker\\build\\web\\assets\\assets/images/bus_location.png", "C:\\Users\\<USER>\\Desktop\\UniTracker\\frontend\\unitracker_front-end\\unitracker\\build\\web\\assets\\assets/images/bus_logo.png", "C:\\Users\\<USER>\\Desktop\\UniTracker\\frontend\\unitracker_front-end\\unitracker\\build\\web\\assets\\assets/images/bus_marker.png", "C:\\Users\\<USER>\\Desktop\\UniTracker\\frontend\\unitracker_front-end\\unitracker\\build\\web\\assets\\assets/images/location_pin.png", "C:\\Users\\<USER>\\Desktop\\UniTracker\\frontend\\unitracker_front-end\\unitracker\\build\\web\\assets\\assets/images/logo.png", "C:\\Users\\<USER>\\Desktop\\UniTracker\\frontend\\unitracker_front-end\\unitracker\\build\\web\\assets\\assets/images/logo2.png", "C:\\Users\\<USER>\\Desktop\\UniTracker\\frontend\\unitracker_front-end\\unitracker\\build\\web\\assets\\assets/images/stop_marker.png", "C:\\Users\\<USER>\\Desktop\\UniTracker\\frontend\\unitracker_front-end\\unitracker\\build\\web\\assets\\assets/fonts/Poppins/Poppins-Regular.ttf", "C:\\Users\\<USER>\\Desktop\\UniTracker\\frontend\\unitracker_front-end\\unitracker\\build\\web\\assets\\assets/fonts/Poppins/Poppins-Medium.ttf", "C:\\Users\\<USER>\\Desktop\\UniTracker\\frontend\\unitracker_front-end\\unitracker\\build\\web\\assets\\assets/fonts/Poppins/Poppins-SemiBold.ttf", "C:\\Users\\<USER>\\Desktop\\UniTracker\\frontend\\unitracker_front-end\\unitracker\\build\\web\\assets\\assets/fonts/Poppins/Poppins-Bold.ttf", "C:\\Users\\<USER>\\Desktop\\UniTracker\\frontend\\unitracker_front-end\\unitracker\\build\\web\\assets\\packages/cupertino_icons/assets/CupertinoIcons.ttf", "C:\\Users\\<USER>\\Desktop\\UniTracker\\frontend\\unitracker_front-end\\unitracker\\build\\web\\assets\\fonts/MaterialIcons-Regular.otf", "C:\\Users\\<USER>\\Desktop\\UniTracker\\frontend\\unitracker_front-end\\unitracker\\build\\web\\assets\\shaders/ink_sparkle.frag", "C:\\Users\\<USER>\\Desktop\\UniTracker\\frontend\\unitracker_front-end\\unitracker\\build\\web\\assets\\AssetManifest.json", "C:\\Users\\<USER>\\Desktop\\UniTracker\\frontend\\unitracker_front-end\\unitracker\\build\\web\\assets\\AssetManifest.bin", "C:\\Users\\<USER>\\Desktop\\UniTracker\\frontend\\unitracker_front-end\\unitracker\\build\\web\\assets\\AssetManifest.bin.json", "C:\\Users\\<USER>\\Desktop\\UniTracker\\frontend\\unitracker_front-end\\unitracker\\build\\web\\assets\\FontManifest.json", "C:\\Users\\<USER>\\Desktop\\UniTracker\\frontend\\unitracker_front-end\\unitracker\\build\\web\\assets\\NOTICES", "C:\\Users\\<USER>\\Desktop\\UniTracker\\frontend\\unitracker_front-end\\unitracker\\build\\web\\favicon.png", "C:\\Users\\<USER>\\Desktop\\UniTracker\\frontend\\unitracker_front-end\\unitracker\\build\\web\\icons\\Icon-192.png", "C:\\Users\\<USER>\\Desktop\\UniTracker\\frontend\\unitracker_front-end\\unitracker\\build\\web\\icons\\Icon-512.png", "C:\\Users\\<USER>\\Desktop\\UniTracker\\frontend\\unitracker_front-end\\unitracker\\build\\web\\icons\\Icon-maskable-192.png", "C:\\Users\\<USER>\\Desktop\\UniTracker\\frontend\\unitracker_front-end\\unitracker\\build\\web\\icons\\Icon-maskable-512.png", "C:\\Users\\<USER>\\Desktop\\UniTracker\\frontend\\unitracker_front-end\\unitracker\\build\\web\\manifest.json"]}