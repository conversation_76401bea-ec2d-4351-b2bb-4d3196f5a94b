1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.unitracker"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\main\AndroidManifest.xml:4:5-66
15-->C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\main\AndroidManifest.xml:4:22-64
16    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
16-->C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\main\AndroidManifest.xml:5:5-78
16-->C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\main\AndroidManifest.xml:5:22-76
17    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
17-->C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\main\AndroidManifest.xml:6:5-80
17-->C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\main\AndroidManifest.xml:6:22-78
18    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
18-->C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\main\AndroidManifest.xml:7:5-84
18-->C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\main\AndroidManifest.xml:7:22-82
19    <!--
20 Required to query activities that can process text, see:
21         https://developer.android.com/training/package-visibility and
22         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
23
24         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
25    -->
26    <queries>
26-->C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\main\AndroidManifest.xml:49:5-58:15
27        <intent>
27-->C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\main\AndroidManifest.xml:50:9-53:18
28            <action android:name="android.intent.action.PROCESS_TEXT" />
28-->C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\main\AndroidManifest.xml:51:13-72
28-->C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\main\AndroidManifest.xml:51:21-70
29
30            <data android:mimeType="text/plain" />
30-->C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\main\AndroidManifest.xml:52:13-50
30-->C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\main\AndroidManifest.xml:52:19-48
31        </intent>
32        <intent>
32-->C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\main\AndroidManifest.xml:54:9-57:18
33            <action android:name="android.intent.action.SENDTO" />
33-->C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\main\AndroidManifest.xml:55:13-66
33-->C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\main\AndroidManifest.xml:55:21-64
34
35            <data android:scheme="mailto" />
35-->C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\main\AndroidManifest.xml:52:13-50
36        </intent>
37        <!-- Needs to be explicitly declared on Android R+ -->
38        <package android:name="com.google.android.apps.maps" />
38-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\53a2ed9340247206ebd5bc6be2c3c6ab\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
38-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\53a2ed9340247206ebd5bc6be2c3c6ab\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
39    </queries> <!-- Include required permissions for Google Maps API to run. -->
40    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
40-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\53a2ed9340247206ebd5bc6be2c3c6ab\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
40-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\53a2ed9340247206ebd5bc6be2c3c6ab\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:23:22-76
41
42    <uses-feature
42-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\53a2ed9340247206ebd5bc6be2c3c6ab\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
43        android:glEsVersion="0x00020000"
43-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\53a2ed9340247206ebd5bc6be2c3c6ab\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
44        android:required="true" />
44-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\53a2ed9340247206ebd5bc6be2c3c6ab\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
45
46    <permission
46-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\03633f728d6c6cb243a1b02ac5a56b50\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
47        android:name="com.example.unitracker.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\03633f728d6c6cb243a1b02ac5a56b50\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
48        android:protectionLevel="signature" />
48-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\03633f728d6c6cb243a1b02ac5a56b50\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
49
50    <uses-permission android:name="com.example.unitracker.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
50-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\03633f728d6c6cb243a1b02ac5a56b50\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
50-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\03633f728d6c6cb243a1b02ac5a56b50\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
51
52    <application
53        android:name="android.app.Application"
54        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
54-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\03633f728d6c6cb243a1b02ac5a56b50\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
55        android:debuggable="true"
56        android:extractNativeLibs="true"
57        android:icon="@mipmap/ic_launcher"
58        android:label="unitracker" >
59        <activity
60            android:name="com.example.unitracker.MainActivity"
61            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
62            android:exported="true"
63            android:hardwareAccelerated="true"
64            android:launchMode="singleTop"
65            android:taskAffinity=""
66            android:theme="@style/LaunchTheme"
67            android:windowSoftInputMode="adjustResize" >
68
69            <!--
70                 Specifies an Android theme to apply to this Activity as soon as
71                 the Android process has started. This theme is visible to the user
72                 while the Flutter UI initializes. After that, this theme continues
73                 to determine the Window background behind the Flutter UI.
74            -->
75            <meta-data
76                android:name="io.flutter.embedding.android.NormalTheme"
77                android:resource="@style/NormalTheme" />
78
79            <intent-filter>
80                <action android:name="android.intent.action.MAIN" />
81
82                <category android:name="android.intent.category.LAUNCHER" />
83            </intent-filter>
84        </activity>
85        <!--
86             Don't delete the meta-data below.
87             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
88        -->
89        <meta-data
90            android:name="flutterEmbedding"
91            android:value="2" />
92        <meta-data
93            android:name="com.google.android.geo.API_KEY"
94            android:value="@string/google_maps_key" />
95
96        <provider
96-->[:image_picker_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:9-17:20
97            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
97-->[:image_picker_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-82
98            android:authorities="com.example.unitracker.flutter.image_provider"
98-->[:image_picker_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-74
99            android:exported="false"
99-->[:image_picker_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-37
100            android:grantUriPermissions="true" >
100-->[:image_picker_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-47
101            <meta-data
101-->[:image_picker_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-16:75
102                android:name="android.support.FILE_PROVIDER_PATHS"
102-->[:image_picker_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:15:17-67
103                android:resource="@xml/flutter_image_picker_file_paths" />
103-->[:image_picker_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:16:17-72
104        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
105        <service
105-->[:image_picker_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:19:9-31:19
106            android:name="com.google.android.gms.metadata.ModuleDependencies"
106-->[:image_picker_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:20:13-78
107            android:enabled="false"
107-->[:image_picker_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:21:13-36
108            android:exported="false" >
108-->[:image_picker_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:22:13-37
109            <intent-filter>
109-->[:image_picker_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:24:13-26:29
110                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
110-->[:image_picker_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:25:17-94
110-->[:image_picker_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:25:25-91
111            </intent-filter>
112
113            <meta-data
113-->[:image_picker_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:28:13-30:36
114                android:name="photopicker_activity:0:required"
114-->[:image_picker_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:29:17-63
115                android:value="" />
115-->[:image_picker_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:30:17-33
116        </service>
117        <service
117-->[:location] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:11:9-15:56
118            android:name="com.lyokone.location.FlutterLocationService"
118-->[:location] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-71
119            android:enabled="true"
119-->[:location] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-35
120            android:exported="false"
120-->[:location] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-37
121            android:foregroundServiceType="location" />
121-->[:location] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:15:13-53
122
123        <activity
123-->[:url_launcher_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-11:74
124            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
124-->[:url_launcher_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-74
125            android:exported="false"
125-->[:url_launcher_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-37
126            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" /> <!-- Needs to be explicitly declared on P+ -->
126-->[:url_launcher_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-71
127        <uses-library
127-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\53a2ed9340247206ebd5bc6be2c3c6ab\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
128            android:name="org.apache.http.legacy"
128-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\53a2ed9340247206ebd5bc6be2c3c6ab\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
129            android:required="false" />
129-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\53a2ed9340247206ebd5bc6be2c3c6ab\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
130
131        <activity
131-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\20c0d39cf64344468c94b87daab8227d\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
132            android:name="com.google.android.gms.common.api.GoogleApiActivity"
132-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\20c0d39cf64344468c94b87daab8227d\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
133            android:exported="false"
133-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\20c0d39cf64344468c94b87daab8227d\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
134            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
134-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\20c0d39cf64344468c94b87daab8227d\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
135
136        <meta-data
136-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a04a69dfced0db3dbabcb1482e6ea75f\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
137            android:name="com.google.android.gms.version"
137-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a04a69dfced0db3dbabcb1482e6ea75f\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
138            android:value="@integer/google_play_services_version" />
138-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a04a69dfced0db3dbabcb1482e6ea75f\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
139
140        <uses-library
140-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eb583a217e3f3f03986e9e5d00f3834\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
141            android:name="androidx.window.extensions"
141-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eb583a217e3f3f03986e9e5d00f3834\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
142            android:required="false" />
142-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eb583a217e3f3f03986e9e5d00f3834\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
143        <uses-library
143-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eb583a217e3f3f03986e9e5d00f3834\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
144            android:name="androidx.window.sidecar"
144-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eb583a217e3f3f03986e9e5d00f3834\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
145            android:required="false" />
145-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eb583a217e3f3f03986e9e5d00f3834\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
146
147        <provider
147-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f83f3de6690caa1cf353bc62c54ad57e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
148            android:name="androidx.startup.InitializationProvider"
148-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f83f3de6690caa1cf353bc62c54ad57e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
149            android:authorities="com.example.unitracker.androidx-startup"
149-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f83f3de6690caa1cf353bc62c54ad57e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
150            android:exported="false" >
150-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f83f3de6690caa1cf353bc62c54ad57e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
151            <meta-data
151-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f83f3de6690caa1cf353bc62c54ad57e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
152                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
152-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f83f3de6690caa1cf353bc62c54ad57e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
153                android:value="androidx.startup" />
153-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f83f3de6690caa1cf353bc62c54ad57e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
154            <meta-data
154-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0ef32e5c516841c96415de8496c4ba45\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
155                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
155-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0ef32e5c516841c96415de8496c4ba45\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
156                android:value="androidx.startup" />
156-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0ef32e5c516841c96415de8496c4ba45\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
157        </provider>
158
159        <receiver
159-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0ef32e5c516841c96415de8496c4ba45\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
160            android:name="androidx.profileinstaller.ProfileInstallReceiver"
160-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0ef32e5c516841c96415de8496c4ba45\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
161            android:directBootAware="false"
161-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0ef32e5c516841c96415de8496c4ba45\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
162            android:enabled="true"
162-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0ef32e5c516841c96415de8496c4ba45\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
163            android:exported="true"
163-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0ef32e5c516841c96415de8496c4ba45\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
164            android:permission="android.permission.DUMP" >
164-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0ef32e5c516841c96415de8496c4ba45\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
165            <intent-filter>
165-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0ef32e5c516841c96415de8496c4ba45\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
166                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
166-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0ef32e5c516841c96415de8496c4ba45\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
166-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0ef32e5c516841c96415de8496c4ba45\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
167            </intent-filter>
168            <intent-filter>
168-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0ef32e5c516841c96415de8496c4ba45\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
169                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
169-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0ef32e5c516841c96415de8496c4ba45\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
169-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0ef32e5c516841c96415de8496c4ba45\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
170            </intent-filter>
171            <intent-filter>
171-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0ef32e5c516841c96415de8496c4ba45\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
172                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
172-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0ef32e5c516841c96415de8496c4ba45\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
172-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0ef32e5c516841c96415de8496c4ba45\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
173            </intent-filter>
174            <intent-filter>
174-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0ef32e5c516841c96415de8496c4ba45\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
175                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
175-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0ef32e5c516841c96415de8496c4ba45\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
175-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0ef32e5c516841c96415de8496c4ba45\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
176            </intent-filter>
177        </receiver>
178    </application>
179
180</manifest>
