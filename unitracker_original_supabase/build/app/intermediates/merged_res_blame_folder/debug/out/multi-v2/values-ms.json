{"logs": [{"outputFile": "com.example.unitracker.app-mergeDebugResources-44:/values-ms/values-ms.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\43ad62e77f391830fc09e74f468b5838\\transformed\\browser-1.8.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,260,379", "endColumns": "104,99,118,101", "endOffsets": "155,255,374,476"}, "to": {"startLines": "55,57,58,59", "startColumns": "4,4,4,4", "startOffsets": "5968,6162,6262,6381", "endColumns": "104,99,118,101", "endOffsets": "6068,6257,6376,6478"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\03633f728d6c6cb243a1b02ac5a56b50\\transformed\\core-1.13.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,459,565,683,798", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "145,247,344,454,560,678,793,894"}, "to": {"startLines": "29,30,31,32,33,34,35,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2800,2895,2997,3094,3204,3310,3428,6785", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "2890,2992,3089,3199,3305,3423,3538,6881"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d5c9b5c6411afc8a82ea505119a1fa84\\transformed\\appcompat-1.1.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,321,429,516,620,731,810,889,980,1072,1167,1261,1360,1453,1548,1642,1733,1824,1903,2015,2123,2220,2329,2433,2540,2699,2800", "endColumns": "110,104,107,86,103,110,78,78,90,91,94,93,98,92,94,93,90,90,78,111,107,96,108,103,106,158,100,79", "endOffsets": "211,316,424,511,615,726,805,884,975,1067,1162,1256,1355,1448,1543,1637,1728,1819,1898,2010,2118,2215,2324,2428,2535,2694,2795,2875"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,321,429,516,620,731,810,889,980,1072,1167,1261,1360,1453,1548,1642,1733,1824,1903,2015,2123,2220,2329,2433,2540,2699,6705", "endColumns": "110,104,107,86,103,110,78,78,90,91,94,93,98,92,94,93,90,90,78,111,107,96,108,103,106,158,100,79", "endOffsets": "211,316,424,511,615,726,805,884,975,1067,1162,1256,1355,1448,1543,1637,1728,1819,1898,2010,2118,2215,2324,2428,2535,2694,2795,6780"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\20c0d39cf64344468c94b87daab8227d\\transformed\\jetified-play-services-base-18.1.0\\res\\values-ms\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,464,590,692,860,988,1104,1207,1388,1493,1664,1795,1962,2133,2196,2256", "endColumns": "101,168,125,101,167,127,115,102,180,104,170,130,166,170,62,59,78", "endOffsets": "294,463,589,691,859,987,1103,1206,1387,1492,1663,1794,1961,2132,2195,2255,2334"}, "to": {"startLines": "36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3543,3649,3822,3952,4058,4230,4362,4482,4735,4920,5029,5204,5339,5510,5685,5752,5816", "endColumns": "105,172,129,105,171,131,119,106,184,108,174,134,170,174,66,63,82", "endOffsets": "3644,3817,3947,4053,4225,4357,4477,4584,4915,5024,5199,5334,5505,5680,5747,5811,5894"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a04a69dfced0db3dbabcb1482e6ea75f\\transformed\\jetified-play-services-basement-18.1.0\\res\\values-ms\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "44", "startColumns": "4", "startOffsets": "4589", "endColumns": "145", "endOffsets": "4730"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\82a486fade881837561069ff8daac976\\transformed\\preference-1.2.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,174,263,346,485,654,735", "endColumns": "68,88,82,138,168,80,78", "endOffsets": "169,258,341,480,649,730,809"}, "to": {"startLines": "54,56,60,61,64,65,66", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5899,6073,6483,6566,6886,7055,7136", "endColumns": "68,88,82,138,168,80,78", "endOffsets": "5963,6157,6561,6700,7050,7131,7210"}}]}]}