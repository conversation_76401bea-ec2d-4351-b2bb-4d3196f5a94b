-- Merging decision tree log ---
application
INJECTED from C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\main\AndroidManifest.xml:9:5-43:19
INJECTED from C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\debug\AndroidManifest.xml
MERGED from [:image_picker_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-32:19
MERGED from [:image_picker_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-32:19
MERGED from [:location] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:10:5-16:19
MERGED from [:location] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:10:5-16:19
MERGED from [:url_launcher_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-12:19
MERGED from [:url_launcher_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-12:19
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\53a2ed9340247206ebd5bc6be2c3c6ab\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:36:5-42:19
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\53a2ed9340247206ebd5bc6be2c3c6ab\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:36:5-42:19
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\53e14fac63439656e0f8d85c438059ce\transformed\jetified-play-services-location-21.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\53e14fac63439656e0f8d85c438059ce\transformed\jetified-play-services-location-21.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\20c0d39cf64344468c94b87daab8227d\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\20c0d39cf64344468c94b87daab8227d\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\e4fe943830d433a2612b6b88e31b1cff\transformed\jetified-play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\e4fe943830d433a2612b6b88e31b1cff\transformed\jetified-play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a04a69dfced0db3dbabcb1482e6ea75f\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a04a69dfced0db3dbabcb1482e6ea75f\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eb583a217e3f3f03986e9e5d00f3834\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eb583a217e3f3f03986e9e5d00f3834\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f83f3de6690caa1cf353bc62c54ad57e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f83f3de6690caa1cf353bc62c54ad57e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\03633f728d6c6cb243a1b02ac5a56b50\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\03633f728d6c6cb243a1b02ac5a56b50\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0ef32e5c516841c96415de8496c4ba45\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0ef32e5c516841c96415de8496c4ba45\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2b7936f95fe18ff8bf7ed112a895d983\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2b7936f95fe18ff8bf7ed112a895d983\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\628ad5ec098813e90f17b91306d17867\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\628ad5ec098813e90f17b91306d17867\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\df01c0c97e4a5c93eed17973f90d1588\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:7:5-11:19
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\df01c0c97e4a5c93eed17973f90d1588\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:7:5-11:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\03633f728d6c6cb243a1b02ac5a56b50\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:name
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\main\AndroidManifest.xml
manifest
ADDED from C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\main\AndroidManifest.xml:2:1-59:12
MERGED from C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\main\AndroidManifest.xml:2:1-59:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\debug\AndroidManifest.xml:1:1-7:12
MERGED from [:google_maps_flutter_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\google_maps_flutter_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:image_picker_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-34:12
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:location] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-18:12
MERGED from [:permission_handler_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\permission_handler_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:shared_preferences_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\shared_preferences_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:url_launcher_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-14:12
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\53a2ed9340247206ebd5bc6be2c3c6ab\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:17:1-44:12
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\53e14fac63439656e0f8d85c438059ce\transformed\jetified-play-services-location-21.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\20c0d39cf64344468c94b87daab8227d\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\e4fe943830d433a2612b6b88e31b1cff\transformed\jetified-play-services-tasks-18.0.2\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a04a69dfced0db3dbabcb1482e6ea75f\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\82a486fade881837561069ff8daac976\transformed\preference-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5c9b5c6411afc8a82ea505119a1fa84\transformed\appcompat-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\376dcd62bb8495e4321648785e359c20\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\944d1bf93e2a94c1bb7ff384712d6912\transformed\fragment-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\transforms-3\ce17f35b2355a98950a2039811014519\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\transforms-3\796db52723b61ca6ccc3dc35a1041b98\transformed\jetified-activity-1.9.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\43ad62e77f391830fc09e74f468b5838\transformed\browser-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8d06d8ad75b6392e12207d7ae45ff459\transformed\recyclerview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b791a072bfe91e6bdd73b60618916b23\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\24ae844c8b8ed9eefd3a0e1e9826fb52\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eb583a217e3f3f03986e9e5d00f3834\transformed\jetified-window-1.2.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f97e4a075ba1dd70bde89c0468a54a1b\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:17:1-21:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8d2a52564a58ab8ddda92b8b58884419\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d35951af5fc87f6328889550791663d\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\eacbf9e7dfd66bc41248634aa69ff511\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0872050333cf4d6039361ca78d3aea74\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\162c5709e3d8b30fe37e62cd7ddec57c\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\38d6af4b2e3e8a940e034f90d23f4055\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3370c2e1fe68c36463bbfa5225c57e50\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d779fbcb63585f664a60116754019b41\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\727ba430b7ead3e7c64ba1f8950cf8d4\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f83f3de6690caa1cf353bc62c54ad57e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6780e7561168b7d96f56ac9e52bd4bf\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\37a9bedf4ca93aa384283ccce7660887\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9294263e62819ed34229ae232f2fa425\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3c0228e2cd596a6df63dc0405ddadb0f\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c873256a11d32e2ebc1143d72e261c1\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8aea01584018929ae84382acae090e8\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a9094dd250a5005d8e6a642997adda58\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bcb9ce518a965695877729e045244431\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8263a393aa93a1b578b19fbeff705b6a\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\8c156dccc37b2ee2fd32ec03b43ce2d4\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bfd14487b15e71bf872665dcadf8a9a5\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\46a58c8d25eb2e97dfec345e2bce9102\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f1aa1883b210928f70b47344bb3d7464\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c368eb5b00e23a2f599832f89a93af58\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\03633f728d6c6cb243a1b02ac5a56b50\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\transforms-3\95f2776c034cafb7fb3a9a2e063ac7ef\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0ef32e5c516841c96415de8496c4ba45\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2b7936f95fe18ff8bf7ed112a895d983\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2a87b6ac03e63667b138410d5573f205\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\626c7f5b7dde5a43e9c4fd30c78db962\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\628ad5ec098813e90f17b91306d17867\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\dc22aa3f63528c613c69f3eea539541f\transformed\jetified-datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\dc852e895cdc17d79d7c6ad13d963e87\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\d2b6c848da8e232afe9200f955718c83\transformed\jetified-datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\a6567e03f6aa87655bc59bf57618fe23\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0ee4bf6d2bdad68a7ddd1df0c90f7fe9\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ab4ed99e6d46f47093c8c8c828389741\transformed\jetified-core-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3fe5168a9a1eae464d12e4003094f431\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5fb840f85da14d36dffc44e28cfd82ce\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\76d1eab454a393dab4cbf9c836429fa2\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\bcbfd2e57294fddb0237ff6259f28df9\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\df01c0c97e4a5c93eed17973f90d1588\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\transforms-3\7ff995e132d8da205a5a83f914c5a63d\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\debug\AndroidManifest.xml
	android:versionCode
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\main\AndroidManifest.xml:4:5-66
MERGED from C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\main\AndroidManifest.xml:4:5-66
MERGED from C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\main\AndroidManifest.xml:4:5-66
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\53a2ed9340247206ebd5bc6be2c3c6ab\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\53a2ed9340247206ebd5bc6be2c3c6ab\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:24:5-67
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\main\AndroidManifest.xml:4:22-64
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\main\AndroidManifest.xml:5:5-78
MERGED from [:location] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-79
MERGED from [:location] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-79
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\main\AndroidManifest.xml:5:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\main\AndroidManifest.xml:6:5-80
MERGED from [:location] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-81
MERGED from [:location] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-81
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\main\AndroidManifest.xml:6:22-78
uses-permission#android.permission.ACCESS_BACKGROUND_LOCATION
ADDED from C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\main\AndroidManifest.xml:7:5-84
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\main\AndroidManifest.xml:7:22-82
queries
ADDED from C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\main\AndroidManifest.xml:49:5-58:15
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\53a2ed9340247206ebd5bc6be2c3c6ab\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:30:5-34:15
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\53a2ed9340247206ebd5bc6be2c3c6ab\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:30:5-34:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\main\AndroidManifest.xml:50:9-53:18
action#android.intent.action.PROCESS_TEXT
ADDED from C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\main\AndroidManifest.xml:51:13-72
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\main\AndroidManifest.xml:51:21-70
data
ADDED from C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\main\AndroidManifest.xml:52:13-50
	android:mimeType
		ADDED from C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\main\AndroidManifest.xml:52:19-48
intent#action:name:android.intent.action.SENDTO+data:scheme:mailto
ADDED from C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\main\AndroidManifest.xml:54:9-57:18
action#android.intent.action.SENDTO
ADDED from C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\main\AndroidManifest.xml:55:13-66
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\main\AndroidManifest.xml:55:21-64
uses-sdk
INJECTED from C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\debug\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\debug\AndroidManifest.xml
MERGED from [:google_maps_flutter_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\google_maps_flutter_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:google_maps_flutter_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\google_maps_flutter_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:image_picker_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:6:5-44
MERGED from [:image_picker_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:6:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:location] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:location] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\permission_handler_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\permission_handler_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\shared_preferences_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\shared_preferences_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\53a2ed9340247206ebd5bc6be2c3c6ab\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\53a2ed9340247206ebd5bc6be2c3c6ab\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\53e14fac63439656e0f8d85c438059ce\transformed\jetified-play-services-location-21.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\53e14fac63439656e0f8d85c438059ce\transformed\jetified-play-services-location-21.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\20c0d39cf64344468c94b87daab8227d\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\20c0d39cf64344468c94b87daab8227d\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\e4fe943830d433a2612b6b88e31b1cff\transformed\jetified-play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\e4fe943830d433a2612b6b88e31b1cff\transformed\jetified-play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a04a69dfced0db3dbabcb1482e6ea75f\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a04a69dfced0db3dbabcb1482e6ea75f\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\82a486fade881837561069ff8daac976\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\82a486fade881837561069ff8daac976\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5c9b5c6411afc8a82ea505119a1fa84\transformed\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5c9b5c6411afc8a82ea505119a1fa84\transformed\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\376dcd62bb8495e4321648785e359c20\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\376dcd62bb8495e4321648785e359c20\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\944d1bf93e2a94c1bb7ff384712d6912\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\944d1bf93e2a94c1bb7ff384712d6912\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\transforms-3\ce17f35b2355a98950a2039811014519\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\transforms-3\ce17f35b2355a98950a2039811014519\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\transforms-3\796db52723b61ca6ccc3dc35a1041b98\transformed\jetified-activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\transforms-3\796db52723b61ca6ccc3dc35a1041b98\transformed\jetified-activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\43ad62e77f391830fc09e74f468b5838\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\43ad62e77f391830fc09e74f468b5838\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8d06d8ad75b6392e12207d7ae45ff459\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8d06d8ad75b6392e12207d7ae45ff459\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b791a072bfe91e6bdd73b60618916b23\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b791a072bfe91e6bdd73b60618916b23\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\24ae844c8b8ed9eefd3a0e1e9826fb52\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\24ae844c8b8ed9eefd3a0e1e9826fb52\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eb583a217e3f3f03986e9e5d00f3834\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eb583a217e3f3f03986e9e5d00f3834\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f97e4a075ba1dd70bde89c0468a54a1b\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f97e4a075ba1dd70bde89c0468a54a1b\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8d2a52564a58ab8ddda92b8b58884419\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8d2a52564a58ab8ddda92b8b58884419\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d35951af5fc87f6328889550791663d\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d35951af5fc87f6328889550791663d\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\eacbf9e7dfd66bc41248634aa69ff511\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\eacbf9e7dfd66bc41248634aa69ff511\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0872050333cf4d6039361ca78d3aea74\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0872050333cf4d6039361ca78d3aea74\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\162c5709e3d8b30fe37e62cd7ddec57c\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\162c5709e3d8b30fe37e62cd7ddec57c\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\38d6af4b2e3e8a940e034f90d23f4055\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\38d6af4b2e3e8a940e034f90d23f4055\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3370c2e1fe68c36463bbfa5225c57e50\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3370c2e1fe68c36463bbfa5225c57e50\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d779fbcb63585f664a60116754019b41\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d779fbcb63585f664a60116754019b41\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\727ba430b7ead3e7c64ba1f8950cf8d4\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\727ba430b7ead3e7c64ba1f8950cf8d4\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f83f3de6690caa1cf353bc62c54ad57e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f83f3de6690caa1cf353bc62c54ad57e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6780e7561168b7d96f56ac9e52bd4bf\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6780e7561168b7d96f56ac9e52bd4bf\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\37a9bedf4ca93aa384283ccce7660887\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\37a9bedf4ca93aa384283ccce7660887\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9294263e62819ed34229ae232f2fa425\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9294263e62819ed34229ae232f2fa425\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3c0228e2cd596a6df63dc0405ddadb0f\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3c0228e2cd596a6df63dc0405ddadb0f\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c873256a11d32e2ebc1143d72e261c1\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c873256a11d32e2ebc1143d72e261c1\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8aea01584018929ae84382acae090e8\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8aea01584018929ae84382acae090e8\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a9094dd250a5005d8e6a642997adda58\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a9094dd250a5005d8e6a642997adda58\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bcb9ce518a965695877729e045244431\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bcb9ce518a965695877729e045244431\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8263a393aa93a1b578b19fbeff705b6a\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8263a393aa93a1b578b19fbeff705b6a\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\8c156dccc37b2ee2fd32ec03b43ce2d4\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\8c156dccc37b2ee2fd32ec03b43ce2d4\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bfd14487b15e71bf872665dcadf8a9a5\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bfd14487b15e71bf872665dcadf8a9a5\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\46a58c8d25eb2e97dfec345e2bce9102\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\46a58c8d25eb2e97dfec345e2bce9102\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f1aa1883b210928f70b47344bb3d7464\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f1aa1883b210928f70b47344bb3d7464\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c368eb5b00e23a2f599832f89a93af58\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c368eb5b00e23a2f599832f89a93af58\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\03633f728d6c6cb243a1b02ac5a56b50\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\03633f728d6c6cb243a1b02ac5a56b50\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\transforms-3\95f2776c034cafb7fb3a9a2e063ac7ef\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\transforms-3\95f2776c034cafb7fb3a9a2e063ac7ef\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0ef32e5c516841c96415de8496c4ba45\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0ef32e5c516841c96415de8496c4ba45\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2b7936f95fe18ff8bf7ed112a895d983\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2b7936f95fe18ff8bf7ed112a895d983\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2a87b6ac03e63667b138410d5573f205\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2a87b6ac03e63667b138410d5573f205\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\626c7f5b7dde5a43e9c4fd30c78db962\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\626c7f5b7dde5a43e9c4fd30c78db962\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\628ad5ec098813e90f17b91306d17867\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\628ad5ec098813e90f17b91306d17867\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\dc22aa3f63528c613c69f3eea539541f\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\dc22aa3f63528c613c69f3eea539541f\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\dc852e895cdc17d79d7c6ad13d963e87\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\dc852e895cdc17d79d7c6ad13d963e87\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\d2b6c848da8e232afe9200f955718c83\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\d2b6c848da8e232afe9200f955718c83\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\a6567e03f6aa87655bc59bf57618fe23\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\a6567e03f6aa87655bc59bf57618fe23\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0ee4bf6d2bdad68a7ddd1df0c90f7fe9\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0ee4bf6d2bdad68a7ddd1df0c90f7fe9\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ab4ed99e6d46f47093c8c8c828389741\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ab4ed99e6d46f47093c8c8c828389741\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3fe5168a9a1eae464d12e4003094f431\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3fe5168a9a1eae464d12e4003094f431\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5fb840f85da14d36dffc44e28cfd82ce\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5fb840f85da14d36dffc44e28cfd82ce\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\76d1eab454a393dab4cbf9c836429fa2\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\76d1eab454a393dab4cbf9c836429fa2\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\bcbfd2e57294fddb0237ff6259f28df9\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\bcbfd2e57294fddb0237ff6259f28df9\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\df01c0c97e4a5c93eed17973f90d1588\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\df01c0c97e4a5c93eed17973f90d1588\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\transforms-3\7ff995e132d8da205a5a83f914c5a63d\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\transforms-3\7ff995e132d8da205a5a83f914c5a63d\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\android\app\src\debug\AndroidManifest.xml
provider#io.flutter.plugins.imagepicker.ImagePickerFileProvider
ADDED from [:image_picker_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:9-17:20
	android:grantUriPermissions
		ADDED from [:image_picker_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-47
	android:authorities
		ADDED from [:image_picker_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-74
	android:exported
		ADDED from [:image_picker_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-37
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-82
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:image_picker_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-16:75
	android:resource
		ADDED from [:image_picker_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:16:17-72
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:15:17-67
service#com.google.android.gms.metadata.ModuleDependencies
ADDED from [:image_picker_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:19:9-31:19
	android:enabled
		ADDED from [:image_picker_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:21:13-36
	android:exported
		ADDED from [:image_picker_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:22:13-37
	tools:ignore
		ADDED from [:image_picker_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:23:13-40
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:20:13-78
intent-filter#action:name:com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:24:13-26:29
action#com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:25:17-94
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:25:25-91
meta-data#photopicker_activity:0:required
ADDED from [:image_picker_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:28:13-30:36
	android:value
		ADDED from [:image_picker_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:30:17-33
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:29:17-63
service#com.lyokone.location.FlutterLocationService
ADDED from [:location] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:11:9-15:56
	android:enabled
		ADDED from [:location] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-35
	android:exported
		ADDED from [:location] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-37
	android:foregroundServiceType
		ADDED from [:location] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:15:13-53
	android:name
		ADDED from [:location] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-71
activity#io.flutter.plugins.urllauncher.WebViewActivity
ADDED from [:url_launcher_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-11:74
	android:exported
		ADDED from [:url_launcher_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [:url_launcher_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-71
	android:name
		ADDED from [:url_launcher_android] C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-74
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\53a2ed9340247206ebd5bc6be2c3c6ab\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\53a2ed9340247206ebd5bc6be2c3c6ab\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:23:22-76
uses-feature#0x00020000
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\53a2ed9340247206ebd5bc6be2c3c6ab\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
	android:glEsVersion
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\53a2ed9340247206ebd5bc6be2c3c6ab\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\53a2ed9340247206ebd5bc6be2c3c6ab\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
package#com.google.android.apps.maps
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\53a2ed9340247206ebd5bc6be2c3c6ab\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\53a2ed9340247206ebd5bc6be2c3c6ab\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
uses-library#org.apache.http.legacy
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\53a2ed9340247206ebd5bc6be2c3c6ab\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\53a2ed9340247206ebd5bc6be2c3c6ab\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\53a2ed9340247206ebd5bc6be2c3c6ab\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\20c0d39cf64344468c94b87daab8227d\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\20c0d39cf64344468c94b87daab8227d\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\20c0d39cf64344468c94b87daab8227d\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\20c0d39cf64344468c94b87daab8227d\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a04a69dfced0db3dbabcb1482e6ea75f\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\df01c0c97e4a5c93eed17973f90d1588\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:8:9-10:69
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\df01c0c97e4a5c93eed17973f90d1588\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:8:9-10:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a04a69dfced0db3dbabcb1482e6ea75f\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a04a69dfced0db3dbabcb1482e6ea75f\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eb583a217e3f3f03986e9e5d00f3834\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eb583a217e3f3f03986e9e5d00f3834\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eb583a217e3f3f03986e9e5d00f3834\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eb583a217e3f3f03986e9e5d00f3834\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eb583a217e3f3f03986e9e5d00f3834\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eb583a217e3f3f03986e9e5d00f3834\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
provider#androidx.startup.InitializationProvider
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f83f3de6690caa1cf353bc62c54ad57e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0ef32e5c516841c96415de8496c4ba45\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0ef32e5c516841c96415de8496c4ba45\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2b7936f95fe18ff8bf7ed112a895d983\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2b7936f95fe18ff8bf7ed112a895d983\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f83f3de6690caa1cf353bc62c54ad57e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f83f3de6690caa1cf353bc62c54ad57e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f83f3de6690caa1cf353bc62c54ad57e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f83f3de6690caa1cf353bc62c54ad57e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f83f3de6690caa1cf353bc62c54ad57e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f83f3de6690caa1cf353bc62c54ad57e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f83f3de6690caa1cf353bc62c54ad57e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\03633f728d6c6cb243a1b02ac5a56b50\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\03633f728d6c6cb243a1b02ac5a56b50\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\03633f728d6c6cb243a1b02ac5a56b50\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.example.unitracker.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\03633f728d6c6cb243a1b02ac5a56b50\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\03633f728d6c6cb243a1b02ac5a56b50\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\03633f728d6c6cb243a1b02ac5a56b50\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\03633f728d6c6cb243a1b02ac5a56b50\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\03633f728d6c6cb243a1b02ac5a56b50\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.example.unitracker.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\03633f728d6c6cb243a1b02ac5a56b50\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\03633f728d6c6cb243a1b02ac5a56b50\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0ef32e5c516841c96415de8496c4ba45\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0ef32e5c516841c96415de8496c4ba45\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0ef32e5c516841c96415de8496c4ba45\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0ef32e5c516841c96415de8496c4ba45\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0ef32e5c516841c96415de8496c4ba45\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0ef32e5c516841c96415de8496c4ba45\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0ef32e5c516841c96415de8496c4ba45\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0ef32e5c516841c96415de8496c4ba45\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0ef32e5c516841c96415de8496c4ba45\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0ef32e5c516841c96415de8496c4ba45\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0ef32e5c516841c96415de8496c4ba45\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0ef32e5c516841c96415de8496c4ba45\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0ef32e5c516841c96415de8496c4ba45\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0ef32e5c516841c96415de8496c4ba45\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0ef32e5c516841c96415de8496c4ba45\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0ef32e5c516841c96415de8496c4ba45\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0ef32e5c516841c96415de8496c4ba45\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0ef32e5c516841c96415de8496c4ba45\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0ef32e5c516841c96415de8496c4ba45\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0ef32e5c516841c96415de8496c4ba45\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0ef32e5c516841c96415de8496c4ba45\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
