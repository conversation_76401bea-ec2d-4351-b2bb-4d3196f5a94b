Flutter crash report.
Please report a bug at https://github.com/flutter/flutter/issues.

## command

flutter pub get --no-example

## exception

PathAccessException: PathAccessException: Deletion failed, path = 'C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\windows\flutter\ephemeral\.plugin_symlinks' (OS Error: The process cannot access the file because it is being used by another process.
, errno = 32)

```
#0      _Directory._deleteSync (dart:io/directory_impl.dart:188:7)
#1      FileSystemEntity.deleteSync (dart:io/file_system_entity.dart:407:7)
#2      ForwardingFileSystemEntity.deleteSync (package:file/src/forwarding/forwarding_file_system_entity.dart:70:16)
#3      ErrorHandlingDirectory.deleteSync.<anonymous closure> (package:flutter_tools/src/base/error_handling_io.dart:492:22)
#4      _runSync (package:flutter_tools/src/base/error_handling_io.dart:600:14)
#5      ErrorHandlingDirectory.deleteSync (package:flutter_tools/src/base/error_handling_io.dart:491:12)
#6      ErrorHandlingFileSystem.deleteIfExists (package:flutter_tools/src/base/error_handling_io.dart:84:14)
#7      _createPlatformPluginSymlinks (package:flutter_tools/src/flutter_plugins.dart:976:29)
#8      createPluginSymlinks (package:flutter_tools/src/flutter_plugins.dart:912:5)
#9      refreshPluginsList (package:flutter_tools/src/flutter_plugins.dart:1027:5)
<asynchronous suspension>
#10     FlutterProject.ensureReadyForPlatformSpecificTooling (package:flutter_tools/src/project.dart:373:5)
<asynchronous suspension>
#11     PackagesGetCommand.runCommand (package:flutter_tools/src/commands/packages.dart:386:7)
<asynchronous suspension>
#12     FlutterCommand.run.<anonymous closure> (package:flutter_tools/src/runner/flutter_command.dart:1450:27)
<asynchronous suspension>
#13     AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:153:19)
<asynchronous suspension>
#14     CommandRunner.runCommand (package:args/command_runner.dart:212:13)
<asynchronous suspension>
#15     FlutterCommandRunner.runCommand.<anonymous closure> (package:flutter_tools/src/runner/flutter_command_runner.dart:421:9)
<asynchronous suspension>
#16     AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:153:19)
<asynchronous suspension>
#17     FlutterCommandRunner.runCommand (package:flutter_tools/src/runner/flutter_command_runner.dart:364:5)
<asynchronous suspension>
#18     run.<anonymous closure>.<anonymous closure> (package:flutter_tools/runner.dart:131:9)
<asynchronous suspension>
#19     AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:153:19)
<asynchronous suspension>
#20     main (package:flutter_tools/executable.dart:94:3)
<asynchronous suspension>
```

## flutter doctor

```
[!] Flutter (Channel stable, 3.27.3, on Microsoft Windows [Version 10.0.22631.5262], locale en-US)
    • Flutter version 3.27.3 on channel stable at C:\Users\<USER>\Flutter\src\flutter
    ! Warning: `flutter` on your path resolves to C:\Users\<USER>\flutter_windows_3.27.4-stable\flutter\bin\flutter, which is not inside your current Flutter SDK checkout at C:\Users\<USER>\Flutter\src\flutter. Consider adding C:\Users\<USER>\Flutter\src\flutter\bin to the front of your path.
    ! Warning: `dart` on your path resolves to C:\Users\<USER>\flutter_windows_3.27.4-stable\flutter\bin\dart, which is not inside your current Flutter SDK checkout at C:\Users\<USER>\Flutter\src\flutter. Consider adding C:\Users\<USER>\Flutter\src\flutter\bin to the front of your path.
    • Upstream repository https://github.com/flutter/flutter.git
    • Framework revision c519ee916e (4 months ago), 2025-01-21 10:32:23 -0800
    • Engine revision e672b006cb
    • Dart version 3.6.1
    • DevTools version 2.40.2
    • If those were intentional, you can disregard the above warnings; however it is recommended to use "git" directly to perform update checks and upgrades.

[✓] Windows Version (Installed version of Windows is version 10 or higher)

[✓] Android toolchain - develop for Android devices (Android SDK version 35.0.1)
    • Android SDK at C:\Users\<USER>\AppData\Local\Android\Sdk
    • Platform android-35, build-tools 35.0.1
    • ANDROID_SDK_ROOT = C:\Users\<USER>\AppData\Local\Android\Sdk
    • Java binary at: C:\Program Files\Android\Android Studio\jbr\bin\java
    • Java version OpenJDK Runtime Environment (build 21.0.5+-13047016-b750.29)
    • All Android licenses accepted.

[✓] Chrome - develop for the web
    • Chrome at C:\Program Files\Google\Chrome\Application\chrome.exe

[✓] Visual Studio - develop Windows apps (Visual Studio Build Tools 2019 16.11.42)
    • Visual Studio at C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools
    • Visual Studio Build Tools 2019 version 16.11.35425.106
    • Windows 10 SDK version 10.0.19041.0

[✓] Android Studio (version 2024.2)
    • Android Studio at C:\Program Files\Android\Android Studio
    • Flutter plugin can be installed from:
      🔨 https://plugins.jetbrains.com/plugin/9212-flutter
    • Dart plugin can be installed from:
      🔨 https://plugins.jetbrains.com/plugin/6351-dart
    • android-studio-dir = C:\Program Files\Android\Android Studio
    • Java version OpenJDK Runtime Environment (build 21.0.5+-13047016-b750.29)

[✓] VS Code (version 1.99.3)
    • VS Code at C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code
    • Flutter extension version 3.106.0

[✓] Connected device (3 available)
    • Windows (desktop) • windows • windows-x64    • Microsoft Windows [Version 10.0.22631.5262]
    • Chrome (web)      • chrome  • web-javascript • Google Chrome 136.0.7103.114
    • Edge (web)        • edge    • web-javascript • Microsoft Edge 135.0.3179.85

[✓] Network resources
    • All expected network resources are available.

! Doctor found issues in 1 category.
```
