Flutter crash report.
Please report a bug at https://github.com/flutter/flutter/issues.

## command

flutter pub get

## exception

PathAccessException: PathAccessException: Deletion failed, path = 'C:\Users\<USER>\AndroidStudioProjects\studioprojects\unitracker_front-end\unitracker\windows\flutter\ephemeral\.plugin_symlinks' (OS Error: The process cannot access the file because it is being used by another process.
, errno = 32)

```
#0      _Directory._deleteSync (dart:io/directory_impl.dart:226:7)
#1      FileSystemEntity.deleteSync (dart:io/file_system_entity.dart:425:7)
#2      ForwardingFileSystemEntity.deleteSync (package:file/src/forwarding/forwarding_file_system_entity.dart:70:16)
#3      ErrorHandlingDirectory.deleteSync.<anonymous closure> (package:flutter_tools/src/base/error_handling_io.dart:449:22)
#4      _runSync (package:flutter_tools/src/base/error_handling_io.dart:550:14)
#5      ErrorHandlingDirectory.deleteSync (package:flutter_tools/src/base/error_handling_io.dart:448:12)
#6      ErrorHandlingFileSystem.deleteIfExists (package:flutter_tools/src/base/error_handling_io.dart:90:14)
#7      _createPlatformPluginSymlinks (package:flutter_tools/src/flutter_plugins.dart:1097:29)
#8      createPluginSymlinks (package:flutter_tools/src/flutter_plugins.dart:1027:5)
#9      refreshPluginsList (package:flutter_tools/src/flutter_plugins.dart:1167:5)
<asynchronous suspension>
#10     FlutterProject.ensureReadyForPlatformSpecificTooling (package:flutter_tools/src/project.dart:368:5)
<asynchronous suspension>
#11     PackagesGetCommand.runCommand (package:flutter_tools/src/commands/packages.dart:388:7)
<asynchronous suspension>
#12     FlutterCommand.run.<anonymous closure> (package:flutter_tools/src/runner/flutter_command.dart:1558:27)
<asynchronous suspension>
#13     AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:154:19)
<asynchronous suspension>
#14     CommandRunner.runCommand (package:args/command_runner.dart:212:13)
<asynchronous suspension>
#15     FlutterCommandRunner.runCommand.<anonymous closure> (package:flutter_tools/src/runner/flutter_command_runner.dart:496:9)
<asynchronous suspension>
#16     AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:154:19)
<asynchronous suspension>
#17     FlutterCommandRunner.runCommand (package:flutter_tools/src/runner/flutter_command_runner.dart:431:5)
<asynchronous suspension>
#18     run.<anonymous closure>.<anonymous closure> (package:flutter_tools/runner.dart:98:11)
<asynchronous suspension>
#19     AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:154:19)
<asynchronous suspension>
#20     main (package:flutter_tools/executable.dart:99:3)
<asynchronous suspension>
```

## flutter doctor

```
[32m[✓][39m Flutter (Channel stable, 3.29.3, on Microsoft Windows [Version 10.0.22631.5262], locale en-US) [660ms]
    [32m•[39m Flutter version 3.29.3 on channel stable at C:\Users\<USER>\flutter_windows_3.27.4-stable\flutter
    [32m•[39m Upstream repository https://github.com/flutter/flutter.git
    [32m•[39m Framework revision ea121f8859 (6 weeks ago), 2025-04-11 19:10:07 +0000
    [32m•[39m Engine revision cf56914b32
    [32m•[39m Dart version 3.7.2
    [32m•[39m DevTools version 2.42.3

[32m[✓][39m Windows Version (11 Pro 64-bit, 23H2, 2009) [4.2s]

[32m[✓][39m Android toolchain - develop for Android devices (Android SDK version 35.0.1) [5.8s]
    [32m•[39m Android SDK at C:\Users\<USER>\AppData\Local\Android\Sdk
    [32m•[39m Platform android-35, build-tools 35.0.1
    [32m•[39m ANDROID_SDK_ROOT = C:\Users\<USER>\AppData\Local\Android\Sdk
    [32m•[39m Java binary at: C:\Program Files\Android\Android Studio\jbr\bin\java
      This is the JDK bundled with the latest Android Studio installation on this machine.
      To manually set the JDK path, use: `flutter config --jdk-dir="path/to/jdk"`.
    [32m•[39m Java version OpenJDK Runtime Environment (build 21.0.5+-13047016-b750.29)
    [32m•[39m All Android licenses accepted.

[32m[✓][39m Chrome - develop for the web [181ms]
    [32m•[39m Chrome at C:\Program Files\Google\Chrome\Application\chrome.exe

[32m[✓][39m Visual Studio - develop Windows apps (Visual Studio Build Tools 2019 16.11.42) [179ms]
    [32m•[39m Visual Studio at C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools
    [32m•[39m Visual Studio Build Tools 2019 version 16.11.35425.106
    [32m•[39m Windows 10 SDK version 10.0.19041.0

[32m[✓][39m Android Studio (version 2024.2) [19ms]
    [32m•[39m Android Studio at C:\Program Files\Android\Android Studio
    [32m•[39m Flutter plugin can be installed from:
      🔨 https://plugins.jetbrains.com/plugin/9212-flutter
    [32m•[39m Dart plugin can be installed from:
      🔨 https://plugins.jetbrains.com/plugin/6351-dart
    [32m•[39m android-studio-dir = C:\Program Files\Android\Android Studio
    [32m•[39m Java version OpenJDK Runtime Environment (build 21.0.5+-13047016-b750.29)

[32m[✓][39m VS Code (version 1.99.3) [18ms]
    [32m•[39m VS Code at C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code
    [32m•[39m Flutter extension version 3.106.0

[32m[✓][39m Connected device (3 available) [233ms]
    [32m•[39m Windows (desktop) • windows • windows-x64    • Microsoft Windows [Version 10.0.22631.5262]
    [32m•[39m Chrome (web)      • chrome  • web-javascript • Google Chrome 136.0.7103.114
    [32m•[39m Edge (web)        • edge    • web-javascript • Microsoft Edge 135.0.3179.85

[32m[✓][39m Network resources [359ms]
    [32m•[39m All expected network resources are available.

[32m•[39m No issues found!
```
