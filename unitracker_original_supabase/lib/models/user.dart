class User {
  final String id;
  final String name;
  final String email;
  final String studentId;
  final String university;
  final String department;
  final String role;
  final String? profileImage;
  final String? driverId;
  final String? licenseNumber;
  final String? licenseExpiration;

  User({
    required this.id,
    required this.name,
    required this.email,
    required this.studentId,
    required this.university,
    required this.department,
    required this.role,
    this.profileImage,
    this.driverId,
    this.licenseNumber,
    this.licenseExpiration,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] as String,
      name: json['full_name'] as String? ?? json['name'] as String? ?? '',
      email: json['email'] as String,
      studentId: json['student_id'] as String? ?? json['studentId'] as String? ?? '',
      university: json['university'] as String? ?? '',
      department: json['department'] as String? ?? '',
      role: json['role'] as String,
      profileImage: json['profile_image_url'] as String? ?? json['profileImage'] as String?,
      driverId: json['driver_id'] as String? ?? json['driverId'] as String?,
      licenseNumber: json['driver_license'] as String? ?? json['licenseNumber'] as String?,
      licenseExpiration: json['license_expiry'] as String? ?? json['licenseExpiration'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'studentId': studentId,
      'university': university,
      'department': department,
      'role': role,
      'profileImage': profileImage,
      'driverId': driverId,
      'licenseNumber': licenseNumber,
      'licenseExpiration': licenseExpiration,
    };
  }

  User copyWith({
    String? id,
    String? name,
    String? email,
    String? studentId,
    String? university,
    String? department,
    String? role,
    String? profileImage,
    String? driverId,
    String? licenseNumber,
    String? licenseExpiration,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      studentId: studentId ?? this.studentId,
      university: university ?? this.university,
      department: department ?? this.department,
      role: role ?? this.role,
      profileImage: profileImage ?? this.profileImage,
      driverId: driverId ?? this.driverId,
      licenseNumber: licenseNumber ?? this.licenseNumber,
      licenseExpiration: licenseExpiration ?? this.licenseExpiration,
    );
  }
}
