import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';
import '../services/auth_service.dart';
import 'dart:async';

class AuthProvider extends ChangeNotifier {
  final AuthService _authService;
  User? _currentUser;
  bool _isLoading = false;
  String? _error;
  late StreamSubscription<User?> _authStateSubscription;

  User? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  bool get isAuthenticated => _currentUser != null;
  String? get error => _error;

  AuthProvider(this._authService) {
    // Remove the temporary dummy user setup
    // _currentUser = User(
    //   id: 'dummy_student_id',
    //   name: 'Dummy Student User',
    //   email: '<EMAIL>',
    //   studentId: 'S12345',
    //   university: 'Dummy University',
    //   department: 'Dummy Department',
    //   role: 'student', // Set role to student
    //   profileImage: null,
    //   driverId: null,
    //   licenseNumber: null,
    //   licenseExpiration: null,
    // );
    // _isLoading = false; // Ensure loading is false immediately
    // notifyListeners(); // Explicitly notify listeners after setting initial state

    // Listen to the auth state changes from AuthService
    _authStateSubscription = _authService.authStateChanges.listen((user) {
      print('Auth state changed from stream: $user'); // Keep for debugging
      _currentUser = user; // Update the current user
      _isLoading = false; // Set loading to false after state change
      _error = null; // Clear any errors on successful auth state change
      notifyListeners(); // Notify listeners of the change
    });

    // Initialize _currentUser based on the current state of AuthService
    // Ensure _currentUser is null initially and relies on authStateChanges
    _currentUser = null; // Explicitly set to null
    _isLoading = false; // Ensure not in loading state initially
  }

  @override
  void dispose() {
    _authStateSubscription.cancel();
    super.dispose();
  }

  Future<bool> login(String email, String password) async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    try {
      await _authService.signIn(email: email, password: password);
      // The stream listener will handle updating _currentUser and notifying listeners
      return true;
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  Future<bool> signup(
    String name,
    String email,
    String password,
    String role, {
    String? studentId,
    String? university,
    String? department,
    String? driverId,
    String? licenseNumber,
    String? licenseExpiration,
  }) async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    try {
      if (role == 'driver') {
        await _authService.signUpDriver(
          name: name,
          email: email,
          driverId: driverId ?? '',
          licenseNumber: licenseNumber ?? '',
          licenseExpirationDate: licenseExpiration ?? '',
          licenseImagePath: '',
          password: password,
        );
      } else {
        await _authService.signUp(
          name: name,
          email: email,
          studentId: studentId ?? '',
          university: university ?? '',
          department: department ?? '',
          password: password,
        );
      }
      // The stream listener will handle updating _currentUser and notifying listeners
      return true;
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  Future<void> logout() async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    try {
      await _authService.signOut();
      // The stream listener will handle updating _currentUser and notifying listeners
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
      rethrow;
    }
  }

  Future<void> resetPassword({
    required String email,
    required String role,
    String? driverId,
  }) async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    try {
      await _authService.resetPassword(
          email: email, role: role, driverId: driverId);
      // No auth state change expected here, notifyListeners handled in AuthService finally
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
      rethrow;
    }
  }

  Future<void> updateProfile({
    String? name,
    String? email,
    String? studentId,
    String? university,
    String? department,
    String? profileImage,
  }) async {
    if (_currentUser == null) return;

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      await _authService.updateProfile(
        name: name,
        email: email,
        studentId: studentId,
        university: university,
        department: department,
        profileImage: profileImage,
      );
      // No auth state change expected here, notifyListeners handled in AuthService finally
    } catch (e) {
      _error = 'Failed to update profile: ${e.toString()}';
      throw Exception(_error);
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    if (_currentUser == null) {
      throw Exception('No user is currently logged in');
    }

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      await _authService.changePassword(
        currentPassword: currentPassword,
        newPassword: newPassword,
      );
      // No auth state change expected here, notifyListeners handled in AuthService finally
    } catch (e) {
      _error = 'Failed to change password: ${e.toString()}';
      throw Exception(_error);
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Remove unused validation methods as they are not part of AuthProvider's core responsibility
  // bool _validateEmail(String email, {String? role}) {
  //   if (role == 'driver' && (email == null || email.isEmpty)) {
  //     return true;
  //   }
  //   final emailRegex = RegExp(r'^[\w\.-]+@([\w-]+\.)+[\w-]{2,4}$');
  //   return emailRegex.hasMatch(email);
  // }

  // bool _validatePassword(String password) {
  //   return password.length >= 6;
  // }
}
