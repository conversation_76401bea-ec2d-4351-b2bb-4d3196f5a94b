import 'package:flutter/material.dart';
import 'package:provider/provider.dart'; // Import Provider
import '../models/driver.dart';
import '../models/driver_stats.dart';
import '../models/driver_activity.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;
import '../providers/auth_provider.dart'; // Import AuthProvider

// TODO: Replace with your actual API base URL
const String _apiBaseUrl =
    'YOUR_API_BASE_URL'; // Ensure this is the same as in AuthService

class DriverProvider extends ChangeNotifier {
  Driver? _driver;
  DriverStats? _stats;
  List<DriverActivity> _activities = [];
  bool _isLoading = false; // Added loading state

  Driver? get driver => _driver;
  DriverStats? get stats => _stats;
  List<DriverActivity> get activities => List.unmodifiable(_activities);
  bool get isLoading => _isLoading; // Expose loading state

  // Removed Mock initialization for demo
  // void loadMockData() { ... }

  // Add a method to fetch driver data. It now takes BuildContext to access AuthProvider.
  Future<void> fetchDriverData(BuildContext context, String driverId) async {
    _isLoading = true;
    notifyListeners();

    try {
      // First, try to get driver data from the authenticated user in AuthProvider
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final authenticatedUser = authProvider.currentUser;

      if (authenticatedUser != null &&
          authenticatedUser.role == 'driver' &&
          authenticatedUser.driverId == driverId) {
        print(
            'Using authenticated user data from AuthProvider for driver profile.');
        // Use data from the authenticated user to create Driver and DriverStats
        _driver = Driver(
          id: authenticatedUser.id, // Use user ID as driver ID
          name: authenticatedUser.name,
          driverId: authenticatedUser.driverId ??
              '', // Use driverId from user, fallback to empty
          licenseNumber: authenticatedUser.licenseNumber ??
              '', // Use licenseNumber from user, fallback to empty
          licenseExpirationDate: authenticatedUser.licenseExpiration != null
              ? (DateTime.tryParse(authenticatedUser.licenseExpiration!) ??
                  DateTime
                      .now()) // Parse date string, fallback to now if parsing fails
              : DateTime.now(), // Fallback to now if date is null
          licenseImagePath: authenticatedUser.profileImage ??
              '', // Use profileImage as licenseImagePath for now
          profileImage: authenticatedUser.profileImage,
          isAvailable: true, // Default to true
          currentRouteId: null,
          currentBusId: null,
          isLocalImage: false, // Assuming not a local image for now
        );
        _stats = DriverStats(
          driverId: driverId,
          totalTrips: 0, // Default stats for a new user
          rating: 0.0, // Default stats
          onTimePerformance: 0.0, // Default stats - Set to 0.0
          safetyScore: 0.0, // Default stats - Set to 0.0
          customerSatisfaction: 0.0, // Default stats - Set to 0.0
          joinedDate: DateTime.now(), // Default joined date
        );
        print(
            'Driver and Stats created from authenticated user data. Notifying listeners.');
        // Data is available from authenticated user, no need for API call or fallback dummy data
      } else {
        print(
            'Authenticated user is not a driver or does not match driverId, or user is null. Attempting API fetch (or fallback).');
        // If not using authenticated user, proceed with API fetch (which will likely fail) or fallback
        // TODO: Replace with actual API call to get driver profile and stats
        final url = Uri.parse(
            '$_apiBaseUrl/drivers/$driverId/profile'); // Placeholder URL
        print('Attempting to fetch driver data from API: $url'); // Log the URL
        final response = await http.get(
          url,
          headers: {
            'Content-Type': 'application/json',
            // TODO: Include Authorization header with driver token if needed
            // 'Authorization': 'Bearer your_token',
          },
        );

        print(
            'API Response Status Code: ${response.statusCode}'); // Log status code
        print('API Response Body: ${response.body}'); // Log response body

        if (response.statusCode == 200) {
          // TODO: Handle successful response - API returns driver profile and stats
          final dynamic responseData = jsonDecode(response.body);
          // Assuming the response contains both driver and stats data
          _driver = Driver.fromJson(responseData['driver']
              as Map<String, dynamic>); // Placeholder parsing
          _stats = DriverStats.fromJson(responseData['stats']
              as Map<String, dynamic>); // Placeholder parsing
          // TODO: Fetch activities as well if needed
          print(
              'Driver data fetched and parsed successfully from API. Notifying listeners.'); // Log success
        } else {
          // TODO: Handle API errors
          debugPrint(
              'Error fetching driver data from API: ${response.statusCode} ${response.reasonPhrase}');
          print(
              'Error fetching driver data from API: ${response.statusCode} ${response.reasonPhrase}'); // Log error
          // Optionally parse error message from response.body
          try {
            final errorData = jsonDecode(response.body);
            if (errorData != null && errorData['message'] != null) {
              debugPrint('Error message: ${errorData['message']}');
              print(
                  'Error message from API: ${errorData['message']}'); // Log error message from API
            }
          } catch (e) {
            // Ignore parsing errors
          }
          _driver = null; // Clear driver data on error
          _stats = null; // Clear stats data on error

          // Temporary: Create a dummy driver profile if API call fails
          _createDummyDriverProfile(driverId); // Fallback to dummy data

          throw Exception('Failed to fetch driver data from API');
        }
      }
    } catch (e) {
      debugPrint(
          'An error occurred while fetching driver data: ${e.toString()}');
      print(
          'Exception caught during driver data fetch: ${e.toString()}'); // Log exception
      _driver = null; // Clear driver data on error
      _stats = null; // Clear stats data on error

      // Temporary: Create a dummy driver profile if an exception occurs
      _createDummyDriverProfile(driverId); // Fallback to dummy data

      rethrow; // Re-throw the exception after attempting dummy data creation
    } finally {
      _isLoading = false;
      notifyListeners(); // Notify listeners after state changes regardless of success or failure
      print(
          'fetchDriverData finished. _driver is now: $_driver'); // Log final state of _driver
    }
  }

  void updateProfile({
    String? name,
    String? licenseNumber,
    DateTime? licenseExpirationDate,
    String? profileImage,
    bool? isLocalImage,
  }) {
    if (_driver != null) {
      _driver = _driver!.copyWith(
        name: name,
        licenseNumber: licenseNumber,
        licenseExpirationDate: licenseExpirationDate,
        profileImage: profileImage,
        isLocalImage: isLocalImage,
      );
      notifyListeners();
    }
  }

  // Removed setDriver and setStats - data should be fetched via fetchDriverData after login
  // void setDriver(Driver driver) { ... }
  // void setStats(DriverStats stats) { ... }

  void addActivity(DriverActivity activity) {
    _activities.insert(0, activity); // Add to the top
    notifyListeners();
  }

  // Temporary method to create a dummy driver profile
  void _createDummyDriverProfile(String driverId) {
    print(
        'Creating dummy driver profile for ID: $driverId (fallback)'); // Log dummy creation as fallback
    _driver = Driver(
      id: driverId,
      name: 'Fallback Dummy Driver', // Clearly mark as fallback dummy data
      driverId: driverId,
      licenseNumber: 'FALLBACK123', // Dummy license number
      licenseExpirationDate:
          DateTime.now().add(const Duration(days: 30)), // Dummy expiration date
      licenseImagePath: '', // Dummy image path
      profileImage: null,
      isAvailable: true,
      currentRouteId: null,
      currentBusId: null,
      isLocalImage: false,
    );
    _stats = DriverStats(
      driverId: driverId,
      totalTrips: 0,
      rating: 0.0,
      onTimePerformance: 0.0,
      safetyScore: 0.0,
      customerSatisfaction: 0.0,
      joinedDate: DateTime.now(),
    );
    _activities = []; // Clear activities for dummy data
    print(
        'Fallback dummy driver profile created: $_driver'); // Log dummy object
  }

  // Add more methods as needed (e.g., fetch from API, update stats, etc.)
}
