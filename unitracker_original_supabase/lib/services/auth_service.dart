import 'package:flutter/foundation.dart';
import 'package:unitracker/models/user.dart' as app_user;
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:unitracker/config/supabase_config.dart';
import 'dart:async';

class AuthService extends ChangeNotifier {
  app_user.User? _currentUser;
  bool _isLoading = false;
  String? _error;
  final SupabaseClient _supabase = Supabase.instance.client;

  // StreamController to manage authentication state changes
  final _authStateController = StreamController<app_user.User?>.broadcast();

  // Stream to listen to authentication state changes
  Stream<app_user.User?> get authStateChanges => _authStateController.stream;

  app_user.User? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  bool get isAuthenticated => _currentUser != null;

  // Initialize auth state
  Future<void> initialize() async {
    try {
      final session = _supabase.auth.currentSession;
      if (session?.user != null) {
        // Get user profile from users table
        final userProfile = await _supabase
            .from(SupabaseConfig.usersTable)
            .select()
            .eq('id', session!.user.id)
            .single();

        _currentUser = app_user.User.fromJson(userProfile);
        _authStateController.add(_currentUser);
      }
    } catch (e) {
      print('Error initializing auth: $e');
    }
    notifyListeners();
  }

  // Dispose the stream controller when the service is no longer needed
  @override
  void dispose() {
    _authStateController.close();
    super.dispose();
  }

  Future<void> signIn({
    required String email,
    required String password,
  }) async {
    _isLoading = true;

    try {
      // Sign in with Supabase
      final response = await _supabase.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (response.user != null) {
        // Get user profile from users table
        final userProfile = await _supabase
            .from(SupabaseConfig.usersTable)
            .select()
            .eq('id', response.user!.id)
            .single();

        _currentUser = app_user.User.fromJson(userProfile);
        _error = null;
        _authStateController.add(_currentUser);
      } else {
        _currentUser = null;
        _error = 'Login failed: Invalid credentials';
        _authStateController.add(_currentUser);
        throw Exception(_error);
      }
    } catch (e) {
      _currentUser = null;
      _error = 'An error occurred during login: ${e.toString()}';
      _authStateController.add(_currentUser);
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> signUp({
    required String name,
    required String email,
    required String studentId,
    required String university,
    required String department,
    required String password,
  }) async {
    _isLoading = true;

    try {
      // Sign up with Supabase Auth
      final response = await _supabase.auth.signUp(
        email: email,
        password: password,
      );

      if (response.user != null) {
        // Create user profile in users table
        await _supabase.from(SupabaseConfig.usersTable).insert({
          'id': response.user!.id,
          'email': email,
          'full_name': name,
          'student_id': studentId,
          'university': university,
          'department': department,
          'role': 'student',
          'is_active': true,
        });

        _currentUser = app_user.User(
          id: response.user!.id,
          name: name,
          email: email,
          studentId: studentId,
          university: university,
          department: department,
          role: 'student',
          profileImage: null,
          driverId: '',
          licenseNumber: '',
          licenseExpiration: '',
        );
        _authStateController.add(_currentUser);
      }
    } catch (e) {
      _currentUser = null;
      _authStateController.add(_currentUser);
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> signUpDriver({
    required String name,
    required String email,
    required String driverId,
    required String licenseNumber,
    required String licenseExpirationDate,
    required String licenseImagePath,
    required String password,
  }) async {
    _isLoading = true;

    try {
      // Sign up with Supabase Auth
      final response = await _supabase.auth.signUp(
        email: email,
        password: password,
      );

      if (response.user != null) {
        // Create driver profile in users table
        await _supabase.from(SupabaseConfig.usersTable).insert({
          'id': response.user!.id,
          'email': email,
          'full_name': name,
          'role': 'driver',
          'driver_license': licenseNumber,
          'license_expiry': licenseExpirationDate,
          'is_active': true,
        });

        _currentUser = app_user.User(
          id: response.user!.id,
          name: name,
          email: email,
          studentId: '',
          university: '',
          department: '',
          role: 'driver',
          profileImage: null,
          driverId: driverId,
          licenseNumber: licenseNumber,
          licenseExpiration: licenseExpirationDate,
        );
        _authStateController.add(_currentUser);
      }
    } catch (e) {
      _currentUser = null;
      _authStateController.add(_currentUser);
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> signOut() async {
    _isLoading = true;

    try {
      await _supabase.auth.signOut();
      _currentUser = null;
      _authStateController.add(_currentUser);
    } catch (e) {
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> updateProfile({
    String? name,
    String? email,
    String? studentId,
    String? university,
    String? department,
    String? profileImage,
  }) async {
    if (_currentUser == null) return;

    _isLoading = true;

    try {
      // Update user profile in Supabase
      await _supabase.from(SupabaseConfig.usersTable).update({
        if (name != null) 'full_name': name,
        if (email != null) 'email': email,
        if (studentId != null) 'student_id': studentId,
        if (university != null) 'university': university,
        if (department != null) 'department': department,
        if (profileImage != null) 'profile_image_url': profileImage,
      }).eq('id', _currentUser!.id);

      _currentUser = _currentUser!.copyWith(
        name: name ?? _currentUser!.name,
        email: email ?? _currentUser!.email,
        studentId: studentId ?? _currentUser!.studentId,
        university: university ?? _currentUser!.university,
        department: department ?? _currentUser!.department,
        profileImage: profileImage ?? _currentUser!.profileImage,
      );
      _authStateController.add(_currentUser);
    } catch (e) {
      _error = 'Failed to update profile: ${e.toString()}';
      throw Exception(_error);
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    if (_currentUser == null) {
      throw Exception('No user is currently logged in');
    }

    _isLoading = true;

    try {
      await _supabase.auth.updateUser(
        UserAttributes(password: newPassword),
      );
    } catch (e) {
      _error = 'Failed to change password: ${e.toString()}';
      throw Exception(_error);
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> resetPassword({
    required String email,
    required String role,
    String? driverId,
  }) async {
    if (role == 'driver' && (driverId == null || driverId.isEmpty)) {
      throw Exception('Please enter your driver ID');
    }
    if (email.isEmpty) {
      throw Exception('Please enter your email address');
    }

    _isLoading = true;

    try {
      await _supabase.auth.resetPasswordForEmail(email);
    } catch (e) {
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
}
