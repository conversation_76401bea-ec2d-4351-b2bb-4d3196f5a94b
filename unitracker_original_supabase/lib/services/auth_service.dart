import 'package:flutter/foundation.dart';
import 'package:unitracker/models/user.dart' as app_user;
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:unitracker/config/supabase_config.dart';
import 'dart:async';

class AuthService extends ChangeNotifier {
  app_user.User? _currentUser;
  bool _isLoading = false;
  String? _error;
  final SupabaseClient _supabase = Supabase.instance.client;

  // StreamController to manage authentication state changes
  final _authStateController = StreamController<app_user.User?>.broadcast();

  // Stream to listen to authentication state changes
  Stream<app_user.User?> get authStateChanges => _authStateController.stream;

  app_user.User? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  bool get isAuthenticated => _currentUser != null;

  // Dispose the stream controller when the service is no longer needed
  @override
  void dispose() {
    _authStateController.close();
    super.dispose();
  }

  Future<void> signIn({
    required String email,
    required String password,
  }) async {
    _isLoading = true;

    try {
      // Sign in with Supabase
      final response = await _supabase.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (response.user != null) {
        // Get user profile from users table
        final userProfile = await _supabase
            .from(SupabaseConfig.usersTable)
            .select()
            .eq('id', response.user!.id)
            .single();

        _currentUser = app_user.User.fromJson(userProfile);
        _error = null;
        _authStateController.add(_currentUser);
      } else {
        _currentUser = null;
        _error = 'Login failed: Invalid credentials';
        _authStateController.add(_currentUser);
        throw Exception(_error);
      }
    } catch (e) {
      _currentUser = null;
      _error = 'An error occurred during login: ${e.toString()}';
      _authStateController.add(_currentUser);
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> signUp({
    required String name,
    required String email,
    required String studentId,
    required String university,
    required String department,
    required String password,
  }) async {
    _isLoading = true;

    try {
      // Sign up with Supabase Auth
      final response = await _supabase.auth.signUp(
        email: email,
        password: password,
      );

      if (response.user != null) {
        // Create user profile in users table
        await _supabase.from(SupabaseConfig.usersTable).insert({
          'id': response.user!.id,
          'email': email,
          'full_name': name,
          'student_id': studentId,
          'university': university,
          'department': department,
          'role': 'student',
          'is_active': true,
        });

        _currentUser = app_user.User(
          id: response.user!.id,
          name: name,
          email: email,
          studentId: studentId,
          university: university,
          department: department,
          role: 'student',
          profileImage: null,
          driverId: '',
          licenseNumber: '',
          licenseExpiration: '',
        );
        _authStateController.add(_currentUser);
      }
    } catch (e) {
      _currentUser = null;
      _authStateController.add(_currentUser);
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> signUpDriver({
    required String name,
    required String email,
    required String driverId,
    required String licenseNumber,
    required String licenseExpirationDate,
    required String licenseImagePath,
    required String password,
  }) async {
    _isLoading = true;
    // notifyListeners(); // Notify after state changes in finally

    try {
      await Future.delayed(const Duration(seconds: 1));
      _currentUser = app_user.User(
        id: driverId,
        name: name,
        email: email,
        studentId: '',
        university: '',
        department: '',
        role: 'driver',
        profileImage: null,
        driverId: driverId,
        licenseNumber: licenseNumber,
        licenseExpiration: licenseExpirationDate,
      );
      _authStateController.add(_currentUser); // Add the new user to the stream
    } catch (e) {
      _currentUser = null; // Ensure _currentUser is null on error
      _authStateController.add(_currentUser); // Add null to stream on error
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners(); // Notify listeners after state changes
    }
  }

  Future<void> signOut() async {
    _isLoading = true;

    try {
      await _supabase.auth.signOut();
      _currentUser = null;
      _authStateController.add(_currentUser);
    } catch (e) {
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> updateProfile({
    String? name,
    String? email,
    String? studentId,
    String? university,
    String? department,
    String? profileImage,
  }) async {
    if (_currentUser == null) return;

    _isLoading = true;
    // notifyListeners(); // Notify after state changes in finally

    try {
      await Future.delayed(const Duration(seconds: 1));

      _currentUser = _currentUser!.copyWith(
        name: name ?? _currentUser!.name,
        email: email ?? _currentUser!.email,
        studentId: studentId ?? _currentUser!.studentId,
        university: university ?? _currentUser!.university,
        department: department ?? _currentUser!.department,
        profileImage: profileImage ?? _currentUser!.profileImage,
      );
      // No need to add to stream here unless profile update implies auth state change,
      // which is usually not the case. AuthStateChanges is for login/logout/initial state.
      // If profile changes should trigger UI updates, the UI should listen to AuthProvider
      // and AuthProvider should notifyListeners when _currentUser changes.
      _authStateController
          .add(_currentUser); // Add the updated user to the stream
    } catch (e) {
      _error = 'Failed to update profile: ${e.toString()}';
      throw Exception(_error);
    } finally {
      _isLoading = false;
      notifyListeners(); // Notify listeners after state changes
    }
  }

  Future<void> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    if (_currentUser == null) {
      throw Exception('No user is currently logged in');
    }

    _isLoading = true;
    // notifyListeners(); // Notify after state changes in finally

    try {
      await Future.delayed(const Duration(seconds: 1));
    } catch (e) {
      _error = 'Failed to change password: ${e.toString()}';
      throw Exception(_error);
    } finally {
      _isLoading = false;
      notifyListeners(); // Notify listeners after state changes
    }
  }

  Future<void> resetPassword({
    required String email,
    required String role,
    String? driverId,
  }) async {
    if (role == 'driver' && (driverId == null || driverId.isEmpty)) {
      throw Exception('Please enter your driver ID');
    }
    if (email.isEmpty) {
      throw Exception('Please enter your email address');
    }

    _isLoading = true;
    // notifyListeners(); // Notify after state changes in finally

    try {
      await Future.delayed(const Duration(seconds: 1));
    } catch (e) {
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners(); // Notify listeners after state changes
    }
  }
}
