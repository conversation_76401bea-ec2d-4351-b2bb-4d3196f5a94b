import 'package:flutter/foundation.dart';
import '../models/driver_stats.dart';
import '../models/driver_activity.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:intl/intl.dart';
import 'package:flutter/material.dart';
import 'package:unitracker/theme/app_theme.dart';

// TODO: Replace with your actual API base URL
const String _apiBaseUrl =
    'YOUR_API_BASE_URL'; // Ensure this is the same as in AuthService

class DriverStatsService extends ChangeNotifier {
  DriverStats? _driverStats;
  List<DriverActivity> _activities = [];
  bool _isLoading = false;

  DriverStats? get driverStats => _driverStats;
  List<DriverActivity> get activities => List.unmodifiable(_activities);
  bool get isLoading => _isLoading;

  // Initialize with mock data for now
  Future<void> loadDriverStats(String driverId) async {
    _isLoading = true;
    notifyListeners();

    try {
      // TODO: Replace with actual API call
      await Future.delayed(const Duration(seconds: 1)); // Simulated delay
      _driverStats = DriverStats.mock();
      _activities = DriverActivity.getMockActivities();
    } catch (e) {
      debugPrint('Error loading driver stats: $e');
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Get recent activities (last 7 days by default)
  List<DriverActivity> getRecentActivities({int days = 7}) {
    final DateTime cutoff = DateTime.now().subtract(Duration(days: days));
    return _activities
        .where((activity) => activity.timestamp.isAfter(cutoff))
        .toList()
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp));
  }

  // Update driver stats (e.g., after completing a trip)
  // This method can now be used for more general stat updates
  Future<void> updateStats({
    int? newTrips,
    double? newRating,
    double? newOnTimePerformance,
    double? newSafetyScore,
    double? newCustomerSatisfaction,
  }) async {
    if (_driverStats == null) return;

    _isLoading = true;
    notifyListeners();

    try {
      // TODO: Replace with actual API call to update general driver stats
      final url = Uri.parse(
          '$_apiBaseUrl/drivers/stats/${_driverStats!.driverId}'); // Placeholder URL
      final response = await http.put(
        // Assuming PUT or PATCH for updates
        url,
        headers: {
          'Content-Type': 'application/json',
          // TODO: Include Authorization header with driver token if needed
          // 'Authorization': 'Bearer your_token',
        },
        body: jsonEncode({
          // Only include fields that are being updated
          if (newTrips != null) 'totalTrips': newTrips,
          if (newRating != null) 'rating': newRating,
          if (newOnTimePerformance != null)
            'onTimePerformance': newOnTimePerformance,
          if (newSafetyScore != null) 'safetyScore': newSafetyScore,
          if (newCustomerSatisfaction != null)
            'customerSatisfaction': newCustomerSatisfaction,
        }),
      );

      if (response.statusCode == 200) {
        // TODO: Handle successful update response (e.g., API returns updated stats)
        final dynamic responseData = jsonDecode(response.body);
        // Assuming the response directly contains the updated stats data
        _driverStats = DriverStats.fromJson(
            responseData as Map<String, dynamic>); // Placeholder parsing
      } else {
        // TODO: Handle API errors
        debugPrint(
            'Error updating driver stats: ${response.statusCode} ${response.reasonPhrase}');
        // Optionally parse error message from response.body
        try {
          final errorData = jsonDecode(response.body);
          if (errorData != null && errorData['message'] != null) {
            debugPrint('Error message: ${errorData['message']}');
          }
        } catch (e) {
          // Ignore parsing errors
        }
        throw Exception('Failed to update driver stats');
      }
    } catch (e) {
      debugPrint('Error updating driver stats: $e');
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Add a new method to record a completed trip
  Future<void> recordTripCompletion({
    required String tripId,
    required bool isSuccessful,
    double? customerRating,
    bool? isOnTime,
  }) async {
    if (_driverStats == null) {
      debugPrint('Cannot record trip completion: Driver stats not loaded');
      return;
    }

    _isLoading = true; // Set loading true for the operation
    notifyListeners();

    try {
      // TODO: Make API call to record trip completion and get updated stats
      final url =
          Uri.parse('$_apiBaseUrl/trips/$tripId/complete'); // Placeholder URL
      final response = await http.post(
        // Assuming POST or PUT for completing a trip
        url,
        headers: {
          'Content-Type': 'application/json',
          // TODO: Include Authorization header with driver token if needed
          // 'Authorization': 'Bearer your_token',
        },
        body: jsonEncode({
          'isSuccessful': isSuccessful,
          if (customerRating != null) 'customerRating': customerRating,
          if (isOnTime != null) 'isOnTime': isOnTime,
          // Add other relevant trip details as needed by your API
        }),
      );

      if (response.statusCode == 200) {
        // TODO: Handle successful response - likely the API returns the updated driver stats
        final dynamic responseData = jsonDecode(response.body);
        // Assuming the response directly contains the updated driver stats data
        _driverStats = DriverStats.fromJson(
            responseData as Map<String, dynamic>); // Placeholder parsing

        // Add activity for completed trip
        _activities.insert(
            0,
            DriverActivity(
              id: 'activity_${DateTime.now().millisecondsSinceEpoch}', // Temporary ID
              type: DriverActivityType.tripCompleted,
              description: isSuccessful
                  ? 'Trip completed successfully'
                  : 'Trip completion failed',
              timestamp: DateTime.now(),
              status: isSuccessful
                  ? DriverActivityStatus.completed
                  : DriverActivityStatus
                      .cancelled, // Adjust status based on success
            ));
      } else {
        // TODO: Handle API errors
        debugPrint(
            'Error recording trip completion: ${response.statusCode} ${response.reasonPhrase}');
        // Optionally parse error message from response.body
        try {
          final errorData = jsonDecode(response.body);
          if (errorData != null && errorData['message'] != null) {
            debugPrint('Error message: ${errorData['message']}');
          }
        } catch (e) {
          // Ignore parsing errors
        }
        throw Exception('Failed to record trip completion');
      }
    } catch (e) {
      debugPrint('Error recording trip completion: ${e.toString()}');
      rethrow;
    } finally {
      _isLoading = false; // Set loading false after the operation
      notifyListeners(); // Notify listeners of state change
    }
  }

  // Add a new activity
  Future<void> addActivity(DriverActivity activity) async {
    _isLoading = true;
    notifyListeners();

    try {
      // TODO: Replace with actual API call
      await Future.delayed(const Duration(milliseconds: 500));
      _activities.insert(0, activity);
    } catch (e) {
      debugPrint('Error adding activity: $e');
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Get performance summary for a specific metric
  double getPerformanceMetric(String metric) {
    if (_driverStats == null) return 0.0;

    switch (metric) {
      case 'onTime':
        return _driverStats!.onTimePerformance;
      case 'safety':
        return _driverStats!.safetyScore;
      case 'satisfaction':
        return _driverStats!.customerSatisfaction;
      default:
        return 0.0;
    }
  }

  // Calculate trip completion rate
  double getTripCompletionRate() {
    if (_activities.isEmpty) return 0.0;

    final completedTrips = _activities
        .where((a) =>
            a.type == DriverActivityType.tripCompleted &&
            a.status == DriverActivityStatus.completed)
        .length;

    final totalTrips = _activities
        .where((a) => a.type == DriverActivityType.tripCompleted)
        .length;

    return totalTrips > 0 ? (completedTrips / totalTrips) * 100 : 0.0;
  }

  // Helper method to determine activity type icon (placeholder)
  IconData _activityIcon(DriverActivityType type) {
    // TODO: Implement actual icon mapping based on DriverActivityType
    switch (type) {
      case DriverActivityType.tripCompleted:
        return Icons.check_circle_outline;
      // Add other cases for different activity types
      default:
        return Icons.info_outline;
    }
  }

  // Helper method to determine activity color (placeholder)
  Color _activityColor(DriverActivityStatus status) {
    // TODO: Implement actual color mapping based on DriverActivityStatus
    switch (status) {
      case DriverActivityStatus.completed:
        return AppTheme.successColor;
      case DriverActivityStatus.inProgress:
        return AppTheme.infoColor;
      case DriverActivityStatus.scheduled:
        return AppTheme.warningColor;
      case DriverActivityStatus.cancelled:
        return AppTheme.errorColor;
      default:
        return AppTheme.secondaryTextColor;
    }
  }

  // Helper method to determine activity background color (placeholder)
  Color _getActivityBackgroundColor(DriverActivityStatus status) {
    // TODO: Implement actual background color mapping
    switch (status) {
      case DriverActivityStatus.completed:
        return AppTheme.successColor.withOpacity(0.1);
      case DriverActivityStatus.inProgress:
        return AppTheme.infoColor.withOpacity(0.1);
      case DriverActivityStatus.scheduled:
        return AppTheme.warningColor.withOpacity(0.1);
      case DriverActivityStatus.cancelled:
        return AppTheme.errorColor.withOpacity(0.1);
      default:
        return AppTheme.secondaryColor.withOpacity(0.1);
    }
  }

  // Helper method to format joined date (placeholder)
  String _formatJoinedDate(DateTime date) {
    // TODO: Implement actual date formatting logic if needed, using intl package
    return DateFormat('MMMM dd, yyyy').format(date);
  }
}
